package com.sandu.xinye.api.v2.cloudfile;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Cloudfile;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CloudfileService {
    public static final CloudfileService me = new CloudfileService();

    public RetKit getCloudfileList(Integer userId) {
        List<Cloudfile> list = getCloudfileListByUserId(userId);
        return RetKit.ok("list", list);
    }

    public RetKit uploadCloudfile(Integer userId, int type, String fileName, String url) {
        fileName = fileName.trim();
        if (isExistCloudfile(fileName, userId)) {
            return RetKit.fail(RetConstant.CODE_FILE_REPEAT, "该文件名称已存在！");
        }
        Cloudfile model = new Cloudfile();
        model.setType(type);
        model.setName(fileName);
        model.setUserId(userId);
        model.setUrl(url);
        model.setCreateTime(new Date());

        List<Cloudfile> list = new ArrayList<Cloudfile>();
        boolean succ = model.save();
        if (succ) {
            list = getCloudfileListByUserId(userId);
        }
        return succ ? RetKit.ok().set("list", list) : RetKit.fail();
    }

    public RetKit deleteCloudfile(int userId, String fileId) {
        Cloudfile model = Cloudfile.dao.findById(fileId);
        if (model == null) {
            return RetKit.fail("参数有误！");
        }
        boolean succ = Db.tx(new IAtom() {

            @Override
            public boolean run() throws SQLException {
                boolean succ = model.delete();
                boolean succ1 = Db.update("delete from cloudfile where userId=?", userId) >= 0;
                return succ && succ1;
            }

        });
        return succ ? RetKit.ok() : RetKit.fail();
    }

    private boolean isExistCloudfile(String name, int userId) {
        Cloudfile isExist = Cloudfile.dao.findFirst("select id from cloudfile where name=? and userId=?",
                name, userId);
        return isExist != null;
    }

    private List<Cloudfile> getCloudfileListByUserId(int userId) {
        List<Cloudfile> list = Cloudfile.dao.find("select id,name,url,type,creatTime from cloudfile where userId=? ",
                userId);
        return list;
    }

}
