package com.sandu.xinye.common.itask;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.cron4j.ITask;
import com.xiaoleilu.hutool.date.DateUtil;

import java.util.Calendar;
import java.util.Date;

public class EveryDayUpdateOpLog implements ITask {
	Prop p = PropKit.use("common_config.txt");

	@Override
	public void run() {
		Integer expireTime = p.getInt("opLogExpireTime");
		LogKit.info("——————————————————删除30天外日志开始—————————————————————");
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -expireTime);
		Date beginOfDay = DateUtil.beginOfDay(cal.getTime());
		Db.delete("delete from operation_log where createTime < ? " , beginOfDay);

		LogKit.info("-------------------删除30天外日志结束-------------------------");
	}

	@Override
	public void stop() {
		// TODO Auto-generated method stub

	}

}
