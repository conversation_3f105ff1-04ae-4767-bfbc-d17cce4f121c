package com.sandu.xinye.api.v2.templet;

import com.jfinal.aop.Before;
import com.sandu.xinye.api.v2.templet.validator.TempletGroupValidator;
import com.sandu.xinye.api.v2.templet.validator.TempletValidator;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;

public class TempletController extends AppController {

    /**
     * @Title: getTempletPage
     * @Description: 获得模板分页
     * @date 2021年11月16日
     * <AUTHOR>
     */
    public void getTempletPage() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        String name = getPara("name");
        String widthRange = getPara("width", "");
        // 加上-1，兼容旧版本不传参数
        String groupId = getPara("groupId");
        System.out.println("========:" + groupId);
        RetKit ret = TempletService.me.getTempletPage(pageNumber, pageSize, name, getUser().getUserId(), groupId, widthRange);
        renderJson(ret);
    }

    /**
     * @Title: addTemplet
     * @Description: 添加模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletValidator.class)
    @OperationLog(modelName="templet")
    public void addTemplet() {
        String name = getPara("name");
        String cover = getPara("cover");
        String gap = getPara("gap");
        Integer height = getParaToInt("height");
        Integer width = getParaToInt("width");
        Integer printDirection = getParaToInt("printDirection");
        Integer paperType = getParaToInt("paperType");
        Integer machineType = getParaToInt("machineType");
        String data = getPara("data");
        String blackLabelGap = getPara("blackLabelGap");
        String blackLabelOffset = getPara("blackLabelOffset");
        Integer cutAfterPrint = getParaToInt("cutAfterPrint", 0);
        Boolean autoRename = getParaToBoolean("autoRename", true);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        // 多排标签
        Integer labelNum = getParaToInt("labelNum", 1);
        String labelGap = getPara("labelGap");
        Integer multiLabelType = getParaToInt("multiLabelType", 0);
        // 模板分组
        Integer groupId = getParaToInt("groupId", -1);

        RetKit ret = TempletService.me.addTemplet(groupId, name, cover, gap, height, width, printDirection, paperType, machineType, data,
                getUser().getUserId(), blackLabelGap, blackLabelOffset, cutAfterPrint, labelNum, labelGap, multiLabelType, autoRename, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: updateTemplet
     * @Description: 编辑模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletValidator.class)
    @OperationLog(modelName="templet")
    public void updateTemplet() {
        String id = getPara("id");
        String name = getPara("name");
        String cover = getPara("cover");
        String gap = getPara("gap");
        Integer height = getParaToInt("height");
        Integer width = getParaToInt("width");
        Integer printDirection = getParaToInt("printDirection");
        Integer paperType = getParaToInt("paperType");
        Integer machineType = getParaToInt("machineType");
        String data = getPara("data");
        String blackLabelGap = getPara("blackLabelGap");
        String blackLabelOffset = getPara("blackLabelOffset");
        Integer cutAfterPrint = getParaToInt("cutAfterPrint", 0);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        // 多排标签
        String labelGap = getPara("labelGap");
        // 多排标签
        Integer multiLabelType = getParaToInt("multiLabelType", 0);

        RetKit ret = TempletService.me.updateTemplet(id, name, cover, gap, height, width, printDirection, paperType, machineType, data,
                getUser().getUserId(), blackLabelGap, blackLabelOffset, cutAfterPrint, labelGap, multiLabelType, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: remove
     * @Description: 删除模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @OperationLog(modelName="templet")
    public void remove() {
        String id = getPara("id");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.remove(id, userId);
        renderJson(ret);
    }

    /**
     * @Title: addGroup
     * @Description: 添加分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    @OperationLog(modelName="templet_group")
    public void addGroup() {
        String name = getPara("name");
        Boolean autoRename = getParaToBoolean("autoRename", false);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        User user = getUser();
        RetKit ret = TempletService.me.addGroup(user, name, autoRename, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: getGroupList
     * @Description: 获得分组列表
     * @date 2021年11月16日
     * <AUTHOR>
     */
    public void getGroupList() {
        RetKit ret = TempletService.me.getGroupList(getUser());
        renderJson(ret);
    }

    /**
     * @Title: moveTempletToGroup
     * @Description: 把模板移动至指定分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @OperationLog(modelName="templet_group")
    public void moveTempletToGroup() {
        String templetId = getPara("templetId");
        String groupId = getPara("groupId");
        RetKit ret = TempletService.me.moveTempletToGroup(templetId, groupId, getUser());
        renderJson(ret);
    }

    /**
     * @Title: updateTempletGroupName
     * @Description: 更新模板分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    @OperationLog(modelName="templet_group")
    public void updateTempletGroupName() {
        String name = getPara("name");
        String id = getPara("id");
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        RetKit ret = TempletService.me.updateTempletGroupName(id, name, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: deleteTempletGroup
     * @Description: 删除分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    @OperationLog(modelName="templet_group")
    public void deleteTempletGroup() {
        String groupId = getPara("groupId");
        RetKit ret = TempletService.me.deleteTempletGroup(groupId, getUser());
        renderJson(ret);
    }


    /**
     * @Title: getShareSession
     * @Description: 获取模板分享session
     * @date 2021年12月23日
     * <AUTHOR>
     */
    @OperationLog(modelName="templet_share")
    public void getShareSession() {
        long templateId = getParaToLong("templateId");
        RetKit ret = TempletService.me.getTemplateShareSession(templateId, getUser());
        renderJson(ret);
    }

    /**
     * @Title: getTempletBySession
     * @Description: 根据session获取模板
     * @date 2021年12月23日
     * <AUTHOR>
     */
    @OperationLog(modelName="templet_share")
    public void getTempletBySession() {
        String session = getPara("session");
        RetKit ret = TempletService.me.getTemplateByShareSession(session, getUser());
        renderJson(ret);
    }

    /**
     * @Description: 判断模板是否存在
     * @date 2023年2月13日
     * <AUTHOR>
     */
    public void exists(){
        String name = getPara("name");

        Boolean result = TempletService.me.isExistTemplet(name, getUser().getUserId());
        RetKit ret = RetKit.ok().set("data", result);
        renderJson(ret);
    }
}
