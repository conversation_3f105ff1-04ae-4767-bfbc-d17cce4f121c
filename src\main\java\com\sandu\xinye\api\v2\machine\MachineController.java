package com.sandu.xinye.api.v2.machine;

import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.RetKit;

public class MachineController extends AppController {

	/**
	 * @Title: getDeviveList
	 * @Description:
	 * @date 2019年3月13日  上午10:52:24
	 * <AUTHOR>
	 */
	@Clear
	public void getDeviveList(){
		String name = getPara("name");
		String locale = getPara("locale");
		RetKit ret = MachineService.me.getDeviveList(name, locale);
		renderJson(ret);
	}
}
