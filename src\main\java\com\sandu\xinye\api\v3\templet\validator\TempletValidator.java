package com.sandu.xinye.api.v3.templet.validator;

import com.jfinal.core.Controller;
import com.sandu.xinye.common.validator.BaseValidator;

public class TempletValidator extends BaseValidator {

	@Override
	protected void validate(Controller c) {
		validateBodyParamNotNull("name", "模板名称");
		validateBodyParamNotNull("cover", "图片");
		validateBodyParamNotNull("data", "标签内容数据");
		validateBodyParamNotNull("gap", "间隙");
		validateBodyParamNotNull("height", "高度");
		validateBodyParamNotNull("width", "宽度");
		validateBodyParamNotNull("printDirection", "打印方向");
		validateBodyParamNotNull("paperType", "纸张类型");
		validateBodyParamNotNull("machineType", "机器类型");

		validateBodyParamInteger("height", "msg", "高度必须传整数");
		validateBodyParamInteger("width", "msg", "宽度必须传整数");
		validateBodyParamInteger("printDirection", "msg", "打印必须传整数");
		validateBodyParamInteger("paperType", "msg", "纸张类型必须传整数");
		validateBodyParamInteger("machineType", "msg", "机器类型必须传整数");
		validateBodyParamLength("name", 32, "msg", "模板名称长度不超过32个字符");
	}

	@Override
	protected void handleError(Controller c) {
		renderErroJson(c);
	}

}
