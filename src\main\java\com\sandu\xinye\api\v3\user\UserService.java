package com.sandu.xinye.api.v3.user;

import com.jfinal.kit.HashKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.sandu.xinye.api.model.XpUserPrefrence;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.kit.v3.RetKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.UserPreference;
import com.sandu.xinye.common.model.UserSession;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;

import java.util.List;
import static com.sandu.xinye.common.constant.Constant.SAFE_MODE_CLOSE;
import static com.sandu.xinye.common.constant.Constant.SAFE_MODE_OPEN;

public class UserService {

	public static final UserService me = new UserService();

	private static  final String BeginTime = "2022-04-08";

	public RetKit list(int pageNumber, int pageSize) {
		Page<User> page = getAppleUsers(pageNumber, pageSize);
		return RetKit.ok("page", page);
	}

	public RetKit updateAppleUser(String oldAppleUserId, String newAppleUserId) {
		User model = User.dao.findFirst("select * from user where appleLoginUserId = ? ", oldAppleUserId);
		if(model == null){
			return RetKit.fail("用户不存在！");
		}

		model.setAppleLoginUserId(newAppleUserId);

		boolean succ = model.update();
		return succ ? RetKit.ok() : RetKit.fail();
	}

	public RetKit updatePhone(String phone, String captcha, Integer userId) {
		User model = User.dao.findById(userId);
		if(model == null){
			return RetKit.fail("用户不存在！");
		}

		String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
		if (!captcha.equals(realCaptcha)) {
			return RetKit.fail("验证码不正确！");
		}

		model.setUserPhone(phone).setUserNickName(phone);

		boolean succ = model.update();
		return succ ? RetKit.ok() : RetKit.fail();
	}

	public RetKit updatePassword(String oldPassword, String newPassword, Integer userId) {
		User model = User.dao.findById(userId);
		if(model == null){
			return RetKit.fail("用户不存在！");
		}

		String salt = model.getSalt();
		String oldPwd = HashKit.sha256(salt + oldPassword);
		if(!model.getUserPass().equals(oldPwd)){
			return RetKit.fail("旧密码不正确！");
		}

		// 修改成新密码
		salt = HashKit.generateSaltForSha256();
		String hashPwd = HashKit.sha256(salt + newPassword);
		boolean succ = model.setSalt(salt).setUserPass(hashPwd).update();
		if (succ) {
			removeCacheAndSession(model.getUserId());
		}

		return succ ? RetKit.ok() : RetKit.fail();
	}

	public Boolean checkAccountExists(String phoneOrEmail) {
		List<User> users = User.dao.find("select * from user where userPhone=?", phoneOrEmail);
		return CollectionUtil.isNotEmpty(users);
	}

	private Page<User> getAppleUsers(int pageNumber, int pageSize) {
		return User.dao.paginate(pageNumber, pageSize, "select userId, appleLoginUserId, userNickName ",
				" from user where appleLoginUserId  is not null and lastLoginTime > ?", BeginTime);
	}

	private void removeCacheAndSession(Integer userId) {
		List<UserSession> list = UserSession.dao.find("select * from user_session where userId=?", userId);
		for (UserSession cus : list) {
			CacheKit.remove(CacheConstant.APP_USER, cus.getSessionId());
		}
		Db.update("delete from user_session where userId=?", userId);
	}
	/**
	 * 修改用户名和头像
	 *
	 * @return
	 */
  public RetKit update(Integer userId, String userNickName, String userImg) {
    User user = User.dao.findById(userId);
    if (user == null) {
        return RetKit.fail("用户不存在!");
    }
    if (StrUtil.isEmpty(userNickName) && StrUtil.isEmpty(userImg)) {
        return RetKit.fail("用户名和头像不能同时为空！");
    }
    if (StrUtil.isNotEmpty(userNickName)) {
        user.setUserNickName(userNickName);
    }
    if(StrUtil.isNotEmpty(userImg)){
        user.setUserImg(userImg);
    }

    try {
        boolean succ = user.update();
        if (succ) {
            return RetKit.ok("data", user.getUserId());
        }
    } catch (Exception ex) {
        return RetKit.fail(ex.getMessage());
    }

    return RetKit.fail("修改用户信息失败！");
}
/**
     * 获取个人的喜好配置
     */
    public RetKit preference(User user) {
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ", user.getUserId());
        return RetKit.ok("preference", preference);
    }
    public RetKit updatePreference(User user, XpUserPrefrence xpUserPrefrence) {
        RetKit retKit;
        int mode = xpUserPrefrence.getSafeMode();
        if (mode == -1 || (mode != SAFE_MODE_OPEN && mode != SAFE_MODE_CLOSE)) {
            retKit = RetKit.fail("安全模式只能传入0或者1！");
            return retKit;
        }
        String captcha = xpUserPrefrence.getCaptcha();
        String phone = xpUserPrefrence.getPhone();
        if (mode == 0) {
            if (StrKit.isBlank(captcha) || StrKit.isBlank(phone)) {
                return RetKit.fail("手机号和验证码不能为空！");
            }
            String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone.trim());
            if (realCaptcha == null) {
                return RetKit.fail("验证码已过期，请重新发送验证码！");
            }
            if (!captcha.trim().equals(realCaptcha)) {
                return RetKit.fail("验证码错误！");
            }
        }
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ", user.getUserId());
        if (preference == null) {
            preference = new UserPreference();
            preference.setUserId(user.getUserId());
            preference.setSafeMode(mode);
            boolean succ = preference.save();
            return succ ? RetKit.ok() : RetKit.fail();
        } else {
            preference.setSafeMode(mode);
            boolean succ = preference.update();
            return succ ? RetKit.ok() : RetKit.fail();
        }
    }

}
