package com.sandu.xinye.common.kit;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.security.GeneralSecurityException;
import java.util.Properties;

import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.sun.mail.util.MailSSLSocketFactory;
import org.apache.log4j.Logger;

/**
 * 飞鸽传书短信验证码工具类
 * 
 * <AUTHOR>
 * @date 2018/8/1
 */
public class EmailKit {

	private static final Logger logger = Logger.getLogger(EmailKit.class);

	public static boolean sendCaptcha(String email, String captcha){
		String emailContent = "尊敬的用户：您好，欢迎使用XPrinter！ 您的注册验证码为: " + captcha + " (有效期1分钟)";
		String emailSubject = "注册验证码";
		return sendMail(email, emailContent, emailSubject);
	}

	/**
	 *
	 * @param to 收件人邮箱
	 * @param text 邮件正文
	 * @param title 标题
	 */
	/* 发送验证信息的邮件 */
	public static boolean sendMail(String to, String text, String title){
		Prop p = PropKit.use("common_config.txt");
		try {
			final Properties props = new Properties();
			props.put("mail.smtp.auth", "true");
			props.put("mail.smtp.host", "smtp.exmail.qq.com");
			props.put("mail.smtp.port", 465);
			props.put("mail.smtp.ssl.enable", true);

			MailSSLSocketFactory sf = null;
			try{
				sf = new MailSSLSocketFactory();
				sf.setTrustAllHosts(true);
			}catch (GeneralSecurityException e1){
				e1.printStackTrace();
			}
			props.put("mail.smtp.ssl.socketFactory", sf);
			props.put("mail.smtp.socketFactory.port", 465);

			// 发件人的账号
			props.put("mail.user", p.get("mail.user"));
			//发件人的密码
			props.put("mail.password",  p.get("mail.password"));

			// 构建授权信息，用于进行SMTP进行身份验证
			Authenticator authenticator = new Authenticator() {
				@Override
				protected PasswordAuthentication getPasswordAuthentication() {
					// 用户名、密码
					String userName = props.getProperty("mail.user");
					String password = props.getProperty("mail.password");
					return new PasswordAuthentication(userName, password);
				}
			};
			// 使用环境属性和授权信息，创建邮件会话
			Session mailSession = Session.getInstance(props, authenticator);
			// 创建邮件消息
			MimeMessage message = new MimeMessage(mailSession);
			// 设置发件人
			String username = props.getProperty("mail.user");
			InternetAddress form = new InternetAddress(username, "XPrinter", "UTF-8");
			message.setFrom(form);

			// 设置收件人
			InternetAddress toAddress = new InternetAddress(to);
			message.setRecipient(Message.RecipientType.TO, toAddress);

			// 设置邮件标题
			message.setSubject(title, "UTF-8");

			// 设置邮件的内容体
			message.setContent(text, "text/html;charset=UTF-8");
			// 发送邮件
			Transport.send(message);
			return true;
		}catch (Exception e){
			e.printStackTrace();
			logger.error("邮箱发送验证码失败：" + e.getMessage());
		}
		return false;
	}

	public static void main(String[] args) throws Exception { // 做测试用
		//填写接收邮箱※
		boolean result = EmailKit.sendCaptcha("<EMAIL>", "888888");
		if(result) {
			System.out.println("邮箱发送验证码成功");
		}else {
			System.out.println("邮箱发送验证码失败！");
		}

	}

}
