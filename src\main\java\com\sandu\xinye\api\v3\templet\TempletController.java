package com.sandu.xinye.api.v3.templet;

import com.jfinal.aop.Before;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.api.v3.templet.validator.TempletGroupValidator;
import com.sandu.xinye.api.v3.templet.validator.TempletShareValidator;
import com.sandu.xinye.api.v3.templet.validator.TempletValidator;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.v3.RetKit;
import com.sandu.xinye.common.model.User;

public class TempletController extends AppController {

    /**
     * @Title: getTempletPage
     * @Description: 获得模板分页
     * @date 2021年11月16日
     * <AUTHOR>
     */
    public void getTempletPage() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        String name = getPara("name");
        String widthRange = getPara("width", "");
        // 加上-1，兼容旧版本不传参数
        String groupId = getPara("groupId");
        String sort= getPara("sort", "");
        int sortType=getParaToInt("sortType",0);
        RetKit ret = TempletService.me.getTempletPage(pageNumber, pageSize, name, getUser().getUserId(), groupId, widthRange,sort,sortType);
        renderJson(ret);
    }

    /**
     * @Title: addTemplet
     * @Description: 添加模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletValidator.class)
    @OperationLog(modelName="templet")
    public void addTemplet() {
        Record bodyPara = getArgsRecord();
        String name = bodyPara.getStr("name");
        String cover = bodyPara.getStr("cover");
        String gap = bodyPara.getStr("gap");
        Integer height = bodyPara.getInt("height");
        Integer width = bodyPara.getInt("width");
        Integer printDirection = bodyPara.getInt("printDirection");
        Integer paperType = bodyPara.getInt("paperType");
        Integer machineType = bodyPara.getInt("machineType");
        String data = bodyPara.getStr("data");
        String blackLabelGap = bodyPara.getStr("blackLabelGap");
        String blackLabelOffset = bodyPara.getStr("blackLabelOffset");
        Integer cutAfterPrint = bodyPara.get("cutAfterPrint", 0);
        Boolean autoRename = bodyPara.get("autoRename", true);
        Boolean canDuplicate = bodyPara.get("duplicate", false);
        // 多排标签
        Integer labelNum = bodyPara.get("labelNum", 1);
        String labelGap = bodyPara.getStr("labelGap");
        Integer multiLabelType = bodyPara.get("multiLabelType", 0);
        // 模板分组
        Integer groupId = bodyPara.get("groupId", -1);
        // labelType字段整型（标签形状）   1.矩形 2.圆角矩形 3.圆
        Integer labelType = bodyPara.get("labelType", 1);

        RetKit ret = TempletService.me.addTemplet(groupId, name, cover, gap, height, width, printDirection, paperType, machineType, data,
                getUser().getUserId(), blackLabelGap, blackLabelOffset, cutAfterPrint, labelNum, labelGap, labelType, multiLabelType, autoRename, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: updateTemplet
     * @Description: 编辑模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletValidator.class)
    @OperationLog(modelName="templet")
    public void updateTemplet() {
        Record bodyPara = getArgsRecord();
        String id = bodyPara.getStr("id");
        String name = bodyPara.getStr("name");
        String cover = bodyPara.getStr("cover");
        String gap = bodyPara.getStr("gap");
        Integer height = bodyPara.getInt("height");
        Integer width = bodyPara.getInt("width");
        Integer printDirection = bodyPara.getInt("printDirection");
        Integer paperType = bodyPara.getInt("paperType");
        Integer machineType = bodyPara.getInt("machineType");
        String data = bodyPara.getStr("data");
        String blackLabelGap = bodyPara.getStr("blackLabelGap");
        String blackLabelOffset = bodyPara.getStr("blackLabelOffset");
        Integer cutAfterPrint = bodyPara.get("cutAfterPrint", 0);
        Boolean canDuplicate = bodyPara.get("duplicate", false);
        // 多排标签
        String labelGap = bodyPara.getStr(("labelGap"));
        Integer multiLabelType = bodyPara.get("multiLabelType", 0);
        // labelType字段整型（标签形状）   1.矩形 2.圆角矩形 3.圆
        Integer labelType = bodyPara.get("labelType", 1);
        String historyId=getPara("historyId", "");

        RetKit ret = TempletService.me.updateTemplet(id, name, cover, gap, height, width, printDirection, paperType, machineType, data,
                getUser().getUserId(), blackLabelGap, blackLabelOffset, cutAfterPrint, labelGap, labelType, multiLabelType, canDuplicate,historyId);
        renderJson(ret);
    }

    /**
     * @Title: remove
     * @Description: 删除模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @OperationLog(modelName="templet")
    public void remove() {
        String id = getPara("id");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.remove(id,userId);
        renderJson(ret);
    }

    /**
     * @Title: addGroup
     * @Description: 添加分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    @OperationLog(modelName="templet")
    public void addGroup() {
        String name = getPara("name");
        Boolean autoRename = getParaToBoolean("autoRename", false);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        User user = getUser();
        RetKit ret = TempletService.me.addGroup(user, name, autoRename, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: getGroupList
     * @Description: 获得分组列表
     * @date 2021年11月16日
     * <AUTHOR>
     */
    public void getGroupList() {
        RetKit ret = TempletService.me.getGroupList(getUser());
        renderJson(ret);
    }

    /**
     * @Title: moveTempletToGroup
     * @Description: 把模板移动至指定分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    public void moveTempletToGroup() {
        String templetId = getPara("templetId");
        String groupId = getPara("groupId");
        RetKit ret = TempletService.me.moveTempletToGroup(templetId, groupId, getUser());
        renderJson(ret);
    }

    /**
     * @Title: updateTempletGroupName
     * @Description: 更新模板分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    public void updateTempletGroupName() {
        String name = getPara("name");
        String id = getPara("id");
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        RetKit ret = TempletService.me.updateTempletGroupName(id, name, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: deleteTempletGroup
     * @Description: 删除分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    @OperationLog(modelName="templet")
    public void deleteTempletGroup() {
        String groupId = getPara("groupId");
        RetKit ret = TempletService.me.deleteTempletGroup(groupId, getUser());
        renderJson(ret);
    }


    /**
     * @Title: getShareSession
     * @Description: 获取模板分享session
     * @date 2021年12月23日
     * <AUTHOR>
     */
    public void getShareSession() {
        long templateId = getParaToLong("templateId");
        RetKit ret = TempletService.me.getTemplateShareSession(templateId, getUser());
        renderJson(ret);
    }

    /**
     * @Title: getTempletBySession
     * @Description: 根据session获取模板
     * @date 2021年12月23日
     * <AUTHOR>
     */
    public void getTempletBySession() {
        String session = getPara("session");
        RetKit ret = TempletService.me.getTemplateByShareSession(session, getUser());
        renderJson(ret);
    }

    /**
     * @Description: 查询模板历史记录（前6条）
     * @date 2023年2月13日
     * <AUTHOR>
     */
    public void history() {
        Integer limit = getParaToInt("limit", 6);

        RetKit ret = TempletService.me.getTempletHistory(getUser().getUserId(), limit);
        renderJson(ret);
    }


    /**
     * @Title: shareTemplet
     * @Description: 分享模板（web端）
     * @date 2022年10月31日
     * <AUTHOR>
     */
    @Before(TempletShareValidator.class)
    @OperationLog(modelName="templet")
    public void share() {
        Record bodyPara = getArgsRecord();
        Integer templetId = bodyPara.getInt("templetId");
        String phone = bodyPara.getStr("phone");
        String shareCode = bodyPara.getStr("shareCode");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.shareTempletByPhoneAndIdentity(userId, templetId, phone, shareCode);
        renderJson(ret);
    }

    /**
     * @Title: getShareTemplet
     * @Description: 获得分享的模板（我的分享或是分享给我）
     * @date 2022年11月29日
     * <AUTHOR>
     */
    public void getShareTemplet() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);

        // shareType - 1 - 我的分享   2- 分享给我
        int shareType = getParaToInt("shareType", 0);

        RetKit ret = TempletService.me.getShareTemplet(pageNumber, pageSize, getUser().getUserId(), shareType);
        renderJson(ret);
    }

    /**
     * @Description: 判断模板是否存在
     * @date 2023年2月13日
     * <AUTHOR>
     */
    public void exists(){
        String name = getPara("name");

        Boolean result = com.sandu.xinye.api.v2.templet.TempletService.me.isExistTemplet(name, getUser().getUserId());
        com.sandu.xinye.common.kit.RetKit ret = com.sandu.xinye.common.kit.RetKit.ok().set("data", result);
        renderJson(ret);
    }

     /**
     * @Title: batchRemove
     * @Description: 批量删除
     * @date 2024/11/25
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet")
    public void removeHistory() {
        Long historyId = getParaToLong("historyId");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.removeHistory(historyId, userId);
        renderJson(ret);
    }
}
