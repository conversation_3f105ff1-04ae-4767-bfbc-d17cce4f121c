package com.sandu.xinye.api.v2.machine;

import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Help;
import com.sandu.xinye.common.model.Machine;

import java.util.List;

public class MachineService {

    public static final MachineService me = new MachineService();

    /**
     * @param name
     * @return
     * @Title: getDeviveList
     * @Description:
     * @date 2019年3月13日 上午10:53:38
     * <AUTHOR>
     */
    public RetKit getDeviveList(String name, String locale) {
        if (StrKit.notBlank(name)) {
            name = "%" + name + "%";
        }

        if (StrKit.isBlank(locale) || locale.equals(Constant.LOCALE_ZH_CN)) {
            SqlPara sqlPara = Db.getSqlPara("app.machine.list", Kv.by("name", name));
            List<Machine> list = Machine.dao.find(sqlPara);
            return RetKit.ok("list", list);
        }

        String sql = String.format("select m.machineId, " +
                " ifnull(i18n.machineName, m.machineName) as machineName, " +
                " m.createTime " +
                " from machine m" +
                " left join machine_i18n i18n on i18n.machineId = m.machineId and i18n.locale = '%s' " +
                " where m.machineName like '%s' " +
                " order by m.createTime asc", locale, StrKit.isBlank(name) ? '%' : name);
        LogKit.error(sql);
        List<Help> list = Help.dao.find(sql);
        return RetKit.ok("list", list);
    }
}
