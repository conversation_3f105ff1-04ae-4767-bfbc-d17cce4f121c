package com.sandu.xinye.api.v3.common;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.admin.v3.upload.UploadService;
import com.sandu.xinye.api.v3.common.validator.CommonValidator;
import com.sandu.xinye.api.v3.user.UserService;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.interceptor.RateLimitInterceptor;
import com.sandu.xinye.common.kit.v3.RetKit;
import com.jfinal.kit.StrKit;

public class CommonController extends AppController {
    @Clear
    public void healthCheck() {
        renderNull();
    }

    @Clear
    @Before({AttackAntiInterceptor.class, RateLimitInterceptor.class})
    public void sendCaptcha() {
        LogKit.info("调用发送验证码接口开始-----------------" + getParaToMap().toJson());
        // IP黑名单检查
        String ip = getIpAddress();
        LogKit.info("IP:" + ip);
        String blacklist = PropKit.use("common_config.txt").get("sms_ip_blacklist", "");
        if (blacklist != null && !blacklist.isEmpty()) {
            String[] blackIps = blacklist.split(",");
            for (String blackIp : blackIps) {
                if (ip.trim().equals(blackIp.trim())) {
                    renderJson(com.sandu.xinye.common.kit.RetKit.fail("当前IP已被禁用，无法发送短信"));
                    return;
                }
            }
        }
        String phone = getPara("phone");
        String email = getPara("email");
        String type = getPara("type");
        Boolean isInternational = getParaToBoolean("intern", false);
        String sign = getHeader("sign");
        LogKit.info("sign:" + sign);
        RetKit ret;

        if (type.equals(Constant.SEND_CAPTCHA_TYPE_UPDATE_PHONE)) {
            // 修改手机号要验证新手机号是否存在
            Boolean exists = false;
            if (!StrKit.isBlank(phone)) {
                exists = UserService.me.checkAccountExists(phone);
            } else {
                exists = UserService.me.checkAccountExists(email);
            }
            if(exists){
                renderJson(RetKit.fail("该账号已注册，请检查"));
                return;
            }
        }
        if (!StrKit.isBlank(phone)) {
            ret = com.sandu.xinye.api.v3.common.CommonService.me.sendCaptcha(phone, type, isInternational, sign);
        } else {
            ret = com.sandu.xinye.api.v3.common.CommonService.me.sendEmailCaptcha(email, type);
        }
        renderJson(ret);
        LogKit.info("调用发送验证码接口结束-----------------");
    }

    @Clear
    public void checkCaptcha() {
        String phone = getPara("phone");
        String captchaKey = phone;
        if (StrKit.isBlank(phone)) {
            // 如果没有传phone参数，获取email参数
            captchaKey = getPara("email");
        }
        String captcha = getPara("captcha");
        RetKit ret = CommonService.me.checkCaptcha(captchaKey, captcha);
        renderJson(ret);
    }

    public void uploadImg() {
        UploadFile uf = null;
        try {
            uf = getFile("file");
        } catch (Exception e) {
            LogKit.error("上传文件失败！" + e.getMessage());
        }

        RetKit ret = UploadService.me.uploadImg(uf);
        renderJson(ret);
    }
}
