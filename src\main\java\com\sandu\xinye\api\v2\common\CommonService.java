package com.sandu.xinye.api.v2.common;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.*;
import com.sandu.xinye.common.model.User;
import org.apache.log4j.Logger;

public class CommonService {
    private static final String CAPTCHA_SIGN_PREFIX = "captcha_sign_";

    public static final CommonService me = new CommonService();

    public RetKit sendCaptcha(String phone, String type, Boolean isInternational, String sign) {
        if (StrKit.isBlank(phone) || type == null || StrKit.isBlank(type)) {
            return RetKit.fail("参数不能为空！");
        }
        if (StrKit.isBlank(sign)) {
            return RetKit.fail("签名不能为空！");
        }
        // sign防重放校验
        String signKey = CAPTCHA_SIGN_PREFIX + sign;
        String usedSign = com.jfinal.plugin.ehcache.CacheKit.get(CacheConstant.CAPTCHA, signKey);
        if (usedSign != null) {
            return RetKit.fail("请求失败！");
        }

        com.jfinal.plugin.ehcache.CacheKit.put(CacheConstant.CAPTCHA, signKey, "1"); // 存入sign，过期时间由缓存配置决定
        if (StrKit.isBlank(phone) || type == null || StrKit.isBlank(type)) {
            return RetKit.fail("参数不能为空！");
        }
//        if (!type.equals(Constant.SEND_CAPTCHA_TYPE_REGISTER) && !type.equals(Constant.SEND_CAPTCHA_TYPE_FORGOT_PWD) && !type.equals(Constant.SEND_CAPTCHA_TYPE_LOGIN)) {
//            return RetKit.fail("type参数有误！");
//        }
        User cu = User.dao.findFirst("select * from user where userPhone=?", phone);
        if (type.equals(Constant.SEND_CAPTCHA_TYPE_REGISTER)) {
            if (cu != null) {
                return RetKit.fail("该手机号码已注册或已绑定！");
            }
        }
        if (type.equals(Constant.SEND_CAPTCHA_TYPE_FORGOT_PWD)) {
            if (cu == null) {
                return RetKit.fail("该手机号码未注册！");
            }
        }
        String isExistCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
        if (isExistCaptcha != null) {
            return RetKit.fail("1分钟内不能重复发送验证码！");
        }
        // 发送验证码
        String captcha = RandomKit.getRandomPsw(4);
        String result = "";
        if (isInternational) {
            if(phone.startsWith(Constant.SMS_PREFIX_TAIWAN)){
                // 台湾地区政策收紧，单独处理
                result = new AliOpenapiKit().SendToTaiwanSms(phone, captcha);
            }else{
                result = new AliOpenapiKit().SendInternationalSMS(phone, captcha);
            }
        } else {
            result = new AliOpenapiKit().SendSMS(phone, captcha);
        }
        if (result.equals("OK")) {
            CacheKit.put(CacheConstant.CAPTCHA, phone, captcha);
            return RetKit.ok();
        } else if (AliBusinessErr.hasError(result)) {
            CacheKit.remove(CacheConstant.CAPTCHA, phone);
            return RetKit.fail(AliBusinessErr.getErrorInfo(result));
        } else {
            CacheKit.remove(CacheConstant.CAPTCHA, phone);
            return RetKit.fail(result);
        }
    }

    public RetKit sendCaptcha(String phone, String type) {
        return sendCaptcha(phone, type, false);
    }

    public RetKit sendCaptcha(String phone, String type, Boolean isInternational) {
        if (StrKit.isBlank(phone) || type == null || StrKit.isBlank(type)) {
            return RetKit.fail("参数不能为空！");
        }
//        if (!type.equals(Constant.SEND_CAPTCHA_TYPE_REGISTER) && !type.equals(Constant.SEND_CAPTCHA_TYPE_FORGOT_PWD) && !type.equals(Constant.SEND_CAPTCHA_TYPE_LOGIN)) {
//            return RetKit.fail("type参数有误！");
//        }
        User cu = User.dao.findFirst("select * from user where userPhone=?", phone);
        if (type.equals(Constant.SEND_CAPTCHA_TYPE_REGISTER)) {
            if (cu != null) {
                return RetKit.fail("该手机号码已注册或已绑定！");
            }
        }
        if (type.equals(Constant.SEND_CAPTCHA_TYPE_FORGOT_PWD)) {
            if (cu == null) {
                return RetKit.fail("该手机号码未注册！");
            }
        }
        String isExistCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
        if (isExistCaptcha != null) {
            return RetKit.fail("1分钟内不能重复发送验证码！");
        }
        // 发送验证码
        String captcha = RandomKit.getRandomPsw(4);
        String result = "";
        if (isInternational) {
            if(phone.startsWith(Constant.SMS_PREFIX_TAIWAN)){
                // 台湾地区政策收紧，单独处理
                result = new AliOpenapiKit().SendToTaiwanSms(phone, captcha);
            }else{
                result = new AliOpenapiKit().SendInternationalSMS(phone, captcha);
            }
        } else {
            result = new AliOpenapiKit().SendSMS(phone, captcha);
        }
        if (result.equals("OK")) {
            CacheKit.put(CacheConstant.CAPTCHA, phone, captcha);
            return RetKit.ok();
        } else if (AliBusinessErr.hasError(result)) {
            CacheKit.remove(CacheConstant.CAPTCHA, phone);
            return RetKit.fail(AliBusinessErr.getErrorInfo(result));
        } else {
            CacheKit.remove(CacheConstant.CAPTCHA, phone);
            return RetKit.fail(result);
        }

    }

    public RetKit checkCaptcha(String phoneOrEmail, String captcha) {
        if (StrKit.isBlank(phoneOrEmail) || StrKit.isBlank(captcha)) {
            return RetKit.fail("手机号和验证码不能为空");
        }
        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phoneOrEmail);
        if (StrKit.isBlank(realCaptcha)) {
            return RetKit.fail("验证码已失效，请重新发送验证码");
        }
        if (realCaptcha.equals(captcha)) {
            return RetKit.ok().setMsg("验证码正确！");
        } else {
            LogKit.info(String.format("checkCaptcha fail - phone:%s,captcha %s", phoneOrEmail, captcha));
            return RetKit.fail("验证失败！");
        }
    }

    public RetKit sendEmailCaptcha(String email, String type) {
        if (StrKit.isBlank(email) || type == null || StrKit.isBlank(type)) {
            return RetKit.fail("参数不能为空！");
        }
        if (!type.equals(Constant.SEND_CAPTCHA_TYPE_REGISTER) && !type.equals(Constant.SEND_CAPTCHA_TYPE_FORGOT_PWD) && !type.equals(Constant.SEND_CAPTCHA_TYPE_LOGIN)) {
            return RetKit.fail("type参数有误！");
        }
        User cu = User.dao.findFirst("select * from user where userPhone=?", email);
        if (type.equals(Constant.SEND_CAPTCHA_TYPE_REGISTER)) {
            if (cu != null) {
                return RetKit.fail("该邮箱已注册或已绑定！");
            }
        }
        if (type.equals(Constant.SEND_CAPTCHA_TYPE_FORGOT_PWD)) {
            if (cu == null) {
                return RetKit.fail("该邮箱未注册！");
            }
        }
        String isExistCaptcha = CacheKit.get(CacheConstant.CAPTCHA, email);
        if (isExistCaptcha != null) {
            return RetKit.fail("1分钟内不能重复发送验证码！");
        }
        // 邮箱发送验证码
        String captcha = RandomKit.getRandomPsw(4);
        try{
            boolean result = EmailKit.sendCaptcha(email, captcha);
            if (result) {
                CacheKit.put(CacheConstant.CAPTCHA, email, captcha);
                return RetKit.ok();
            } else {
                CacheKit.remove(CacheConstant.CAPTCHA, email);
                return RetKit.fail();
            }
        }catch(Exception e){
            return RetKit.fail(e.getMessage());
        }
    }

}
