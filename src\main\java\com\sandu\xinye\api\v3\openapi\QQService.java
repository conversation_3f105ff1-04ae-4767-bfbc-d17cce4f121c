package com.sandu.xinye.api.v3.openapi;

import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.kit.WxKit;
import com.sandu.xinye.common.kit.v3.RetKit;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;


public class QQService {
    public static final QQService me = new QQService();

    /**
     * 微信网页登录，获取accessToken
     *
     * @return
     */
    public RetKit getAuthorizeToken(String code) {
        LogKit.info("getAuthorizeToken");

        try {
            WxOAuth2AccessToken result = WxKit.getOAuthToken(code);
            LogKit.info("getAuthorizeToken: " + result.getAccessToken());
            return RetKit.ok(result);
        } catch (Exception e) {
            LogKit.error("获取微信登录用户信息失败：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        }
    }


    /**
     * 微信网页登录，获取用户信息
     *
     * @return
     */
    public RetKit getQQUserInfo(String code,String redirect_uri) {
        LogKit.info("geQQUserInfo");

        try {
            // 获取token
            WxOAuth2AccessToken result = WxKit.getQQOAuthToken(code,redirect_uri);
            // 获取用户信息
            WxOAuth2UserInfo userInfo = WxKit.getQQUserInfo(result.getAccessToken(), result.getOpenId());
            userInfo.setOpenid(result.getOpenId());
            return RetKit.ok("data", userInfo);
        } catch (Exception e) {
            LogKit.error("获取qq登录用户信息失败：" + e.getMessage());
            return RetKit.fail(e.getMessage());
        }
    }

    public RetKit getAcessToken() {
        try {
            String token = WxKit.getAccessToken();
            return RetKit.ok("data", token);
        } catch (Exception e) {
            LogKit.error("获取微信登录用户信息失败：" + e.getMessage());
            return RetKit.fail(e.getMessage());
        }

    }

    public RetKit getAppUnionId(String openId) {
        try {
            // 1.0 获取移动端token
            String token = WxKit.getMpAccessToken();
            // 2.0 获取用户信息
            // 获取用户信息
            WxOAuth2UserInfo userInfo = WxKit.getWxUserInfo(token, openId);

            return RetKit.ok("data", userInfo.getUnionId());
        } catch (Exception e) {
            LogKit.error("获取微信登录用户信息失败：" + e.getMessage());
            return RetKit.fail(e.getMessage());
        }

    }
}
