package com.sandu.xinye.api.user;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.kit.RetKit;

/**
 * <AUTHOR>
 */
public class UserController extends AppController {
	/**
	 * @Title: list
	 * @Description:  查找苹果用户列表
	 * <AUTHOR>
	 */
	@Clear
	@Before({AttackAntiInterceptor.class})
	public void appleUserList() {
		int pageNumber = getParaToInt("pageNumber", 1);
		int pageSize = getParaToInt("pageSize", 10);
		RetKit ret = UserService.me.list(pageNumber, pageSize);
		renderJson(ret);
	}

	@Clear
	@Before({AttackAntiInterceptor.class})
	public void updateAppleUser() {
		String oldUserId = getPara("oldUserId");
		String newUserId = getPara("newUserId");
		RetKit ret = UserService.me.updateAppleUser(oldUserId, newUserId);
		renderJson(ret);
	}


}
