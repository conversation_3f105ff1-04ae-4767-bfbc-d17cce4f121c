package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseFont<M extends BaseFont<M>> extends Model<M> implements IBean {

	public M setFontId(java.lang.Integer fontId) {
		set("fontId", fontId);
		return (M)this;
	}
	
	public java.lang.Integer getFontId() {
		return getInt("fontId");
	}

	public M setFontName(java.lang.String fontName) {
		set("fontName", fontName);
		return (M)this;
	}
	
	public java.lang.String getFontName() {
		return getStr("fontName");
	}

	public M setFontEnglishName(java.lang.String fontEnglishName) {
		set("fontEnglishName", fontEnglishName);
		return (M)this;
	}
	
	public java.lang.String getFontEnglishName() {
		return getStr("fontEnglishName");
	}

	public M setFontTraditionalName(java.lang.String fontTraditionalName) {
		set("fontTraditionalName", fontTraditionalName);
		return (M)this;
	}
	
	public java.lang.String getFontTraditionalName() {
		return getStr("fontTraditionalName");
	}

	public M setFontKoreanName(java.lang.String fontKoreanName) {
		set("fontKoreanName", fontKoreanName);
		return (M)this;
	}
	
	public java.lang.String getFontKoreanName() {
		return getStr("fontKoreanName");
	}

	public M setFontKind(java.lang.String fontKind) {
		set("fontKind", fontKind);
		return (M)this;
	}
	
	public java.lang.String getFontKind() {
		return getStr("fontKind");
	}

	public M setFontUrl(java.lang.String fontUrl) {
		set("fontUrl", fontUrl);
		return (M)this;
	}
	
	public java.lang.String getFontUrl() {
		return getStr("fontUrl");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
