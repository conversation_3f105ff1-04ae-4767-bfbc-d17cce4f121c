package com.sandu.xinye.api.v3.feedback;

import com.alibaba.fastjson.JSONArray;
import com.jfinal.json.Json;
import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.v3.RetKit;
import io.netty.util.internal.ObjectUtil;

import java.util.Objects;


public class FeedbackController extends AppController {

	/**
	 *
	 * @Title
	 * @Description 添加意见反馈
	 * @Param content 意见反馈内容
	 * @Param userId 登录人id
	 * @return
	 * <AUTHOR>
	 * @time 2019年3月12日下午3:52:41
	 *
	 */
	public void addFeedback() {
		String content = getPara("content");
		String contact = getPara("contact");
		String[] imgList = getParaValues("img");
		String imgJson = "";

		if(Objects.isNull(content)){
			content = "";
		}
		if(!Objects.isNull(imgList) && imgList.length > 0){
			imgJson = Json.getJson().toJson(imgList);
		}
		LogKit.error("imgList"+imgList.length);
		RetKit ret = FeedbackService.me.addFeedBack(getUser().getUserId(), content, contact, imgJson);
		renderJson(ret);
	}

	public void create() {
		Record bodyPara = getArgsRecord();
		String content = bodyPara.getStr("content");
		String contact =  bodyPara.getStr("contact");
		JSONArray imgList = bodyPara.get("img");
		String imgJson = "";

		if(Objects.isNull(content)){
			content = "";
		}
		if(!Objects.isNull(imgList) && imgList.size() > 0){
			imgJson = imgList.toJSONString();
		}
		LogKit.debug("imgList: "+imgList.size());
		RetKit ret = FeedbackService.me.addFeedBack(getUser().getUserId(), content, contact, imgJson);
		renderJson(ret);
	}
}
