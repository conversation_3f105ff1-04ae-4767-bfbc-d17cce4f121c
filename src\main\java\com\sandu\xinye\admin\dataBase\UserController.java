package com.sandu.xinye.admin.dataBase;

import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;

public class UserController extends AdminController {

	@Clear
	public void index() {
		render("/admin_index.html");
	}

	/**
	 * @Title: list
	 * @Description:  用户列表
	 * @date 2020年6月9日  下午5:10:26
	 * <AUTHOR>
	 */
	public void list() {
		int pageNumber = getParaToInt("pageNumber", 1);
		int pageSize = getParaToInt("pageSize", 10);
		RetKit ret = UserService.me.list(pageNumber, pageSize, getParaToMap());
		renderJson(ret);
	}

	/**
	 * @Title: getUserGroupPage
	 * @Description:  查看用户的模板分组列表
	 * @date 2020年6月9日  下午5:10:00
	 * <AUTHOR>
	 */
	public void getUserGroupPage() {
		int pageNumber = getParaToInt("pageNumber", 1);
		int pageSize = getParaToInt("pageSize", 10);
		String userId = getPara("userId");
		RetKit ret = UserService.me.getUserGroupPage(pageNumber, pageSize, userId);
		renderJson(ret);
	}


}
