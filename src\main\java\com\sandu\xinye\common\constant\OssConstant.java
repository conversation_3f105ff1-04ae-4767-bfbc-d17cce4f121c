package com.sandu.xinye.common.constant;


import com.jfinal.kit.PropKit;

public class OssConstant {
	private static final com.jfinal.kit.Prop prop = PropKit.use("aliopenapi_config.txt");

	/**
	 * OSS -CDN 访问域名
	 */
	public static final String CDN_DOMAIN = prop.get("oss.cdn.domain");

	public static final String ENDPOINT = prop.get("oss.endpoint");

	public static final String BUCKET_NAME = prop.get("oss.bucketName");

	public static final String ACCESSKEY_ID = prop.get("oss.accessKeyId");

	public static final String ACCESSKEY_SECRET = prop.get("oss.accessKeySecret");

}
