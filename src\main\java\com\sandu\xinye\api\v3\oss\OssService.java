package com.sandu.xinye.api.v3.oss;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.PutObjectRequest;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.constant.OssConstant;
import com.sandu.xinye.common.kit.*;
import com.sandu.xinye.common.kit.v3.AliOssV3Kit;
import com.xiaoleilu.hutool.date.DateUtil;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;


public class OssService {
    public static final OssService me = new OssService();

    private static final String OSS_OBJECT_PREFIX_IMG = "img/web3";
    private static final String OSS_OBJECT_PREFIX_FILE = "file/web3";


    private static String stsTokenServer = "http://39.108.10.153:7081";
    Prop p = PropKit.use("common_config.txt");

    {
        String fileName = p.get("sqlConfig");
        p.append(fileName);
        stsTokenServer = p.get("stsTokenServer");
    }

    /**
     * 阿里云OSS上传图片
     *
     * @param uf
     * @return
     */
    public RetKit uploadImg(UploadFile uf) {
        LogKit.info("uploadImg=== 上传图片");
        if (uf == null) {
            LogKit.error("=== 上传图片不能为空");
            return RetKit.fail("上传图片不能为空");
        }
        try {
            if (!ImageKit.isImageExtName(uf.getFileName())) {
                LogKit.error("uploadImg=== 文件类型不正确，只支持类型：jpg、jpeg、png、bmp");
                return RetKit.fail("msg", "文件类型不正确，只支持类型：jpg、jpeg、png、bmp");
            }

            String ossFileUrl = AliOssV3Kit.upload(OSS_OBJECT_PREFIX_IMG, uf);

            return RetKit.ok().set("data", ossFileUrl);
        } catch (Exception e) {
            LogKit.error("上传文件失败：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            LogKit.info("清除临时文件" + uf.getFile().getName());
            uf.getFile().delete();
        }
    }

    public RetKit uploadImg(List<UploadFile> ufs) {
        LogKit.info("uploadImg=== 上传图片");
        if (CollectionUtil.isEmpty(ufs)) {
            LogKit.error("=== 上传图片不能为空");
            return RetKit.fail("上传图片不能为空");
        }
        try {
            if (ufs.size() == 1) {
                return uploadImg(ufs.get(0));
            } else {
                List<String> ossFileUrl = AliOssV3Kit.upload(OSS_OBJECT_PREFIX_IMG, ufs);
                return RetKit.ok().set("data", ossFileUrl.toArray());
            }
        } catch (Exception e) {
            LogKit.error("上传文件失败：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            LogKit.info("清除临时文件: " + ufs.stream().map(uf -> uf.getFile().getName()).collect(Collectors.joining(",")));
            ufs.stream().map(uf -> uf.getFile().delete());
        }
    }

    public RetKit uploadExcel(UploadFile uf) {
        LogKit.info("uploadImg=== 上传excel");
        if (uf == null) {
            LogKit.error("=== 上传excel不能为空");
            return RetKit.fail("上传excel不能为空");
        }
        try {
            if (!MyFileKit.isExcelExtName(uf.getFileName())) {
                LogKit.error("uploadImg=== 文件类型不正确，只支持类型：csv、xls、xlsx");
                return RetKit.fail("msg", "文件类型不正确，只支持类型：csv、xls、xlsx");
            }

            String ossFileUrl = AliOssV3Kit.upload(OSS_OBJECT_PREFIX_FILE, uf);

            return RetKit.ok().set("data", ossFileUrl);
        } catch (Exception e) {
            LogKit.error("上传excel失败：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            LogKit.info("清除临时文件" + uf.getFile().getName());
            uf.getFile().delete();
        }
    }

    public RetKit updateExcel(String ossUrl, UploadFile uf) {
        LogKit.info("uploadImg=== 上传excel");
        if (uf == null) {
            LogKit.error("=== 上传excel不能为空");
            return RetKit.fail("上传excel不能为空");
        }
        try {
            if (!MyFileKit.isExcelExtName(uf.getFileName())) {
                LogKit.error("uploadImg=== 文件类型不正确，只支持类型：csv、xls、xlsx");
                return RetKit.fail("msg", "文件类型不正确，只支持类型：csv、xls、xlsx");
            }

            String ossFileUrl = AliOssV3Kit.update(ossUrl, uf);

            return RetKit.ok().set("data", ossFileUrl);
        } catch (Exception e) {
            LogKit.error("上传excel失败：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            LogKit.info("清除临时文件" + uf.getFile().getName());
            uf.getFile().delete();
        }
    }

    public String copyExcel(String ossUrl) throws Exception {
        if (StrUtil.isEmpty(ossUrl)) {
            throw new Exception("excel不能为空！");
        }
        try {
            if (!MyFileKit.isExcelExtName(ossUrl)) {
                throw new Exception("不是合法的excel文件，不能复制！");
            }

            // 随机生成文件名（时间戳）
            String fileName = Long.toString(DateUtil.current(false)) + '.' + MyFileKit.getExtName(ossUrl);
            String objectPrefix = getOssPrefix(ossUrl);
            String copyFileName = objectPrefix + fileName;
            String ossFileUrl = AliOssKit.copyObject(getOssName(ossUrl), copyFileName);

            return ossFileUrl;
        } catch (Exception e) {
            LogKit.error("复制excel失败：" + e.getMessage());
            throw new Exception(e.getMessage());
        }
    }

    /**
     * 根据ossUrl获取文件名ossName
     */
    private static String getOssName(String ossUrl) {
        return ossUrl.replace(OssConstant.CDN_DOMAIN, "");
    }

    private static String getOssPrefix(String ossUrl) {
        String ossName = getOssName(ossUrl);
        return ossName.substring(0, ossName.lastIndexOf("/") + 1);
    }

    /**
     * @return
     * @Title: getAssumeRole
     * @Description:
     * <AUTHOR>
     */
    public RetKit getAssumeRole() {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet request = new HttpGet(stsTokenServer);

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
                System.out.println("API返回结果：" + result);
                String assumeRoleStr = result.replaceAll("\n", "");
                System.out.println("API返回结果：" + assumeRoleStr);
                JSONObject assumeRoleObject = JSONUtil.parseObj(assumeRoleStr);
                if (assumeRoleObject.get("StatusCode").equals("200")) {
                    return RetKit.ok().set("data", assumeRoleObject);
                } else {
                    return RetKit.fail(assumeRoleObject.get("ErrorMessage"));
                }

            } else {
                System.out.println("API请求失败，错误码：" + statusCode);
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }


        return RetKit.fail("请求STS临时凭证失败!");
    }

    public static void main(String[] args) throws ClientException {
        // OSS访问域名。以华东1（杭州）地域为例，填写为https://oss-cn-hangzhou.aliyuncs.com。其它Region请按实际情况填写。
                String endpoint = "https://oss-cn-shenzhen.aliyuncs.com";
        // 从环境变量中获取步骤5生成的临时访问密钥AccessKey ID和AccessKey Secret，非阿里云账号AccessKey ID和AccessKey Secret。
                String accessKeyId = "STS.NUUsgCNeYjie5oAS1ohfVBu7Y";
                String accessKeySecret = "GfSh4Wc1whzphXEjgWCraLD7xH7rq9wmcPrxBpHdBcU9";
        // 从环境变量中获取步骤5生成的安全令牌SecurityToken。
                String securityToken = "CAISsAN1q6Ft5B2yfSjIr5bgON33o7p43auON0nwtzE6ZOl6rbCcuzz2IHhMfXlhAO8cs/00m2hY6v0alqFjU4VEQ1DJd5OWFlbwWlnzDbDasumZsJbZ4f/MQBpUYnyW9cvWZPqDe7G5U/yxalfCuzZuyL/hD1uLVECkNpv7pvwCac8MDCa1cR1MBtpdOmkHr9QBZ0PMKdGnMBPhngi1C1Fz6C59kn9H4L6j2a/s7QHF3nTwzfUJgpn1Ppm8ZtNwAY97VN65psEUEJDMyylN8RNH2b5rhahfg1f9s8qaHkNa7mfHUaXd+cY9KxRiNOpoWfxUrf7tmPpx//TJlpr+1xsKLeBTQimYVJi4xYnmYLrxb49oL++mayWRiIvRbsXP3ll6MS5BBmRjYME8L3J8MxsoRwzBJ7WvkFKwOV/7EvjUif5sjMMukA24oYHSPSCfQrOI3DYAIponc1gyMBobzfASmRCLkYIlmTpFcFX1Je0bBHg2wShoDhlU0iQrh/fb60h0g18CJkSzTcSeONwPmMFGT/9d0AARRG/MYImF4SRvXwELDMgQvuCDVpHX0Zrs0rv7CYYagAEonHEH08u3genFhMndfCbKLfhOw9eTmFcOmDe2Sa/L9r9ynYcRX6SxXb+mCVYDeyFhKNV3ZK/kKFISms+lSuodSmeUj9Brs0rOoGDySOVqeb5ZyCSTkDYEKfG5BmHoM5sZJRaL+pIqdAgXpwmpdLIU9B6WrbmfCz1zXg9+eVo25iAA";

        // 创建OSSClient实例。
                OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, securityToken);
        // 将本地文件exampletest.txt上传至examplebucket。
                PutObjectRequest putObjectRequest = new PutObjectRequest("xinyeoa-private", "xinye/xppm/345.txt", new File("D:\\test\\123.txt"));
                // 读取文件
                GetObjectRequest getObjectRequest = new GetObjectRequest("xinyeoa-private", "xinye/xppm/345.txt");

        // ObjectMetadata metadata = new ObjectMetadata();
        // 上传文件时设置存储类型。
        // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
        // 上传文件时设置读写权限ACL。
        // metadata.setObjectAcl(CannedAccessControlList.Private);
        // putObjectRequest.setMetadata(metadata);

        try {
//            // 上传文件。
//            ossClient.putObject(putObjectRequest);

            // 下载文件。
            ossClient.getObject(getObjectRequest, new File("D:\\test\\345.txt"));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}
