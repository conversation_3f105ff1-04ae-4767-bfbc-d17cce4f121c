package com.sandu.xinye.api.v3.cloudfile;

import com.jfinal.kit.LogKit;
import com.sandu.xinye.api.v3.oss.OssService;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.RegExpKit;
import com.sandu.xinye.common.kit.v3.RetKit;
import com.sandu.xinye.common.model.Cloudfile;
import com.xiaoleilu.hutool.util.ObjectUtil;
import com.xiaoleilu.hutool.util.StrUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;

public class CloudfileService {
    public static final CloudfileService me = new CloudfileService();

    public RetKit getCloudfileList(Integer userId) {
        List<Cloudfile> list = getCloudfileListByUserId(userId);
        return RetKit.ok("list", list);
    }

    public RetKit uploadCloudfile(Integer userId, int type, String fileName, String url, Boolean override) {
        fileName = fileName.trim();
        if (ObjectUtil.isNull(override)) {
            if (isExistCloudfile(fileName, userId)) {
                return RetKit.fail(RetConstant.CODE_FILE_REPEAT, "该文件名称已存在！");
            }

            Cloudfile model = new Cloudfile();
            model.setType(type);
            model.setName(fileName);
            model.setUserId(userId);
            model.setUrl(url);
            boolean succ = model.save();

            List<Cloudfile> list = new ArrayList<Cloudfile>();
            if (succ) {
                list = getCloudfileListByUserId(userId);
            }
            return succ ? RetKit.ok().set("list", list) : RetKit.fail();
        } else {
            if (BooleanUtils.isTrue(override)) {
                // 获取重名文件对象
                Cloudfile model = getCloudfileByName(fileName, userId);
                model.setUrl(url);
                boolean succ = model.update();

                List<Cloudfile> list = new ArrayList<Cloudfile>();
                if (succ) {
                    list = getCloudfileListByUserId(userId);
                }
                return succ ? RetKit.ok().set("list", list) : RetKit.fail();
            } else if (BooleanUtils.isFalse(override)) {
                String noConflictFileName = getNoConflictFileName(userId, fileName, "");
                Cloudfile model = new Cloudfile();
                model.setType(type);
                model.setName(noConflictFileName);
                model.setUserId(userId);
                model.setUrl(url);
                boolean succ = model.save();

                List<Cloudfile> list = new ArrayList<Cloudfile>();
                if (succ) {
                    list = getCloudfileListByUserId(userId);
                }
                return succ ? RetKit.ok().set("list", list) : RetKit.fail();
            }
        }

        return RetKit.fail("请求异常！");
    }

    private String getNoConflictFileName(int userId, String fileName, String extension) {
        if (StrUtil.isNotEmpty(extension)) {
            fileName = fileName + "." + extension;
        }

        String noExtensionFileName = fileName;
        String fileNameExtension = FilenameUtils.getExtension(fileName);
        if (StrUtil.isNotEmpty(fileNameExtension)) {
            noExtensionFileName = fileName.substring(0, fileName.length() - (fileNameExtension.length() + 1));
        }

        if (isExistCloudfile(fileName, userId)) {
            Matcher matcher = RegExpKit.matchRegExp(noExtensionFileName, RegExpKit.CLOUDFILE_RENAME_REGEXP);
            if (matcher != null) {
                try {
                    String needReplaceStr = matcher.group(1);
                    Integer number = Integer.valueOf(matcher.group(2));
                    return getNoConflictFileName(userId, noExtensionFileName.replace(needReplaceStr, "(" + (number + 1) + ")"), fileNameExtension);
                } catch (Exception e) {
                    LogKit.error(e.getMessage());
                    return null;
                }
            } else {
                return getNoConflictFileName(userId, String.format("%s(1)", noExtensionFileName), fileNameExtension);
            }
        }


        return StrUtil.isNotEmpty(extension) ? noExtensionFileName + "." + extension : fileName;
    }

    public RetKit copyCloudfile(int userId, Long fileId) {
        Cloudfile model = Cloudfile.dao.findById(fileId);
        if (model == null) {
            return RetKit.fail("数据不存在或已删除！");
        }

        boolean succ = false;
        try {
            succ = _copyCloudfile(userId, model, model.getName());
            LogKit.error("succ: " + succ);
        } catch (Exception ex) {
            LogKit.error(ex.getMessage());
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    private boolean _copyCloudfile(int userId, Cloudfile model, String fileName) {
        if (StrUtil.isEmpty(fileName)) {
            fileName = model.getName();
        }

        String newUrl = "";
        try {
            newUrl = OssService.me.copyExcel(model.getUrl());
        } catch (Exception ex) {
            LogKit.error(ex.getMessage());
            return false;
        }

        String copyFileName = getNoConflictFileName(userId, fileName, "");
        Cloudfile newModel = new Cloudfile();
        newModel.setId(null);
        newModel.setType(model.getType());
        newModel.setName(copyFileName);
        newModel.setUserId(userId);
        newModel.setUrl(newUrl);

        return newModel.save();
    }

    public RetKit deleteCloudfile(int userId, String fileId) {
        Cloudfile model = Cloudfile.dao.findById(fileId);
        if (model == null) {
            return RetKit.fail("数据不存在或已删除！");
        }
        boolean succ = model.delete();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    private Cloudfile getCloudfileByName(String name, int userId) {
        Cloudfile cloudfile = Cloudfile.dao.findFirst("select * from cloudfile where name=? and userId=?",
                name, userId);
        return cloudfile;
    }

    private boolean isExistCloudfile(String name, int userId) {
        Cloudfile isExist = Cloudfile.dao.findFirst("select id from cloudfile where name=? and userId=?",
                name, userId);
        return isExist != null;
    }

    private List<Cloudfile> getCloudfileListByUserId(int userId) {
        List<Cloudfile> list = Cloudfile.dao.find("select id,name,url,type,createTime from cloudfile where userId=? ",
                userId);
        return list;
    }

}
