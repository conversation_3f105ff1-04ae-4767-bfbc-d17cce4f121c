package com.sandu.xinye.api.v2.cloudfile;

import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.RetKit;

public class CloudfileController extends AppController {

	/***
	 *
	 * @Title 获取我的文件列表
	 * @Description
	 * @return
	 *
	 */
	public void list() {
		RetKit ret = CloudfileService.me.getCloudfileList(getUser().getUserId());
		renderJson(ret);
	}

	/**
	 * @return
	 * @Title
	 * @Description 上传我的文件
	 * @Param name 文件名称
	 * @Param url 文件地址
	 * <AUTHOR>
	 * @date 2021/10/14
	 */
	public void upload() {
		// 暂时只能上传excel
		int type = Constant.FILE_TYPE_EXCEL;
		String name = getPara("name");
		String url = getPara("url");
		RetKit ret = CloudfileService.me.uploadCloudfile(getUser().getUserId(), type, name, url);
		renderJson(ret);
	}

	/**
	 * @return
	 * @Title
	 * @Description 删除文件
	 * @Param id 文件ID
	 * <AUTHOR>
	 * @date 2021/10/14
	 */
	public void delete() {
		String fileId = getPara("id");
		RetKit ret = CloudfileService.me.deleteCloudfile(getUser().getUserId(), fileId);
		renderJson(ret);
	}

}
