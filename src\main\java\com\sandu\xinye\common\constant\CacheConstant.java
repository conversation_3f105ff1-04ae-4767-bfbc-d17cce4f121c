package com.sandu.xinye.common.constant;

import com.jfinal.kit.PropKit;

public class CacheConstant {
	private static final com.jfinal.kit.Prop prop = PropKit.use("common_config.txt");
	{
		prop.append(prop.get("sqlConfig"));
	}

	/*
	 * 后台管理账号缓存
	 */
	public static final String SYS_USER = prop.get("sys_user");
	
	/*
	 * 验证码缓存
	 */
	public static final String CAPTCHA = prop.get("captcha");

	/*
	 * app用户账号缓存
	 */
	public static final String APP_USER = prop.get("app_user");

	/*
	 * 模板分享缓存
	 */
	public static final String TEMPLET_SHARE = prop.get("templet_share");
}
