package com.sandu.xinye.common.kit;

import com.jfinal.kit.HashKit;
import org.apache.log4j.Logger;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;


/**
 * @ClassName: CipherKit
 * @Description: (密码工具类)
 */

public class CipherKit {

    private static final Logger logger = Logger.getLogger(CipherKit.class);

    /**
     * MD5 of  "xprinter@vida"
     */
    private static final String secret = "69DE4E62DB1E56F8";

    public static String sign(Map<String, String> map) {
        StringBuilder builder = new StringBuilder();
        String signStr = "";

        // 加入secret
        map.put("secret", secret);

        Map<String, String> sortMap = new TreeMap();
        sortMap.putAll(map);
        Set<String> keySet = sortMap.keySet();
        Iterator<String> iter = keySet.iterator();
        while (iter.hasNext()) {
            String key = iter.next();
            builder.append(String.format("%s=%s", key, sortMap.get(key)));
            if (iter.hasNext()) {
                builder.append("&");
            }
        }

        logger.info("MD5签名开始---------------- 待签名字符串： " + builder);
        try {
            signStr = HashKit.md5(builder.toString());
        } catch (Exception e) {
            logger.error("MD5签名失败： " + e.getMessage());
        }

        logger.info("MD5签名结束---------------- " + signStr);

        return signStr;
    }

    @SuppressWarnings("unused")
    public static void main(String args[]) {
        Map map = new TreeMap();
        map.put("timestamp", System.currentTimeMillis());
        map.put("ip", "************");

        CipherKit.sign(map);
    }

}
