package com.sandu.xinye.common.interceptor;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.StrKit;
import org.apache.log4j.Logger;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 跨域
 *
 * <AUTHOR>
 * @date 2023/2/23
 */
public class CrossInterceptor implements Interceptor {
    private static final Logger logger = Logger.getLogger(CrossInterceptor.class);

    private static List<String> ALLOW_ORIGINS = new ArrayList<>();

    static {
        ALLOW_ORIGINS.add("http://test.xprinter365.cn");
        ALLOW_ORIGINS.add("http://www.xprinter365.cn");
        ALLOW_ORIGINS.add("http://api.xprinter365.cn");
    }

    @Override
    public void intercept(Invocation inv) {
        Controller c = inv.getController();
        HttpServletResponse response = c.getResponse();
        String origin = c.getRequest().getHeader("Origin");
        // 匹配算法：equals
        if (ALLOW_ORIGINS.contains(origin)) {
            response.addHeader("Access-Control-Allow-Origin", origin);
        }

        response.addHeader("Access-Control-Allow-Headers", "*");
        response.addHeader("Access-Control-Allow-Credentials", "true");

        //多文件上传 getfiles 必须放在最前面 否则无法取到参数
        if (StrKit.notBlank(response.getContentType()) && response.getContentType().contains("multipart/form-data")) {
            c.getFile();
            c.getFiles();
        }

        inv.invoke();

    }

}
