package com.sandu.xinye.admin.templet;

import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.AESKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.SysUser;
import com.sandu.xinye.common.model.Templet;

import java.util.Date;

public class TempletService {
	public static final TempletService me = new TempletService();

	public RetKit list(int pageSize, int pageNumber, Kv kv) {
		String groupId = kv.getStr("groupId");
		String templetName = kv.getStr("templateName");
		if (StrKit.notBlank(groupId)) {
			kv.set("groupId", groupId);
		}
		if (StrKit.notBlank(templetName)) {
			templetName = "%" + templetName + "%";
			kv.set("name", templetName);
		}
		SqlPara sqlPara = Db.getSqlPara("admin.templet.paginate", kv);
		Page<Templet> page = Templet.dao.paginate(pageNumber, pageSize, sqlPara);

		// 循环解密cover
		String coverFileName = "";
		for (Templet tpl : page.getList()) {
			String cover = tpl.getCover();
			int coverFileNameIndex = cover.lastIndexOf("/");
			if(coverFileNameIndex == -1) {
				LogKit.info("coverFileNameIndex == -1, cover" + cover);
			}
			coverFileName =  cover.substring(coverFileNameIndex+1);
			coverFileName = AESKit.decrypt(coverFileName);
			if(coverFileNameIndex == -1){
				tpl.setCover(cover);
			}else {
				tpl.setCover(cover.substring(0, coverFileNameIndex) + "/" + coverFileName);
			}

		}

		return RetKit.ok("page", page);
	}

	public RetKit add(Templet templet, SysUser sysUser, String ip) {
		String cover = templet.getCover();
		int coverFileNameIndex = cover.lastIndexOf("/");
		String coverFileName = cover.substring(coverFileNameIndex+1);
		try{
			coverFileName = AESKit.encrypt(coverFileName);
		}catch (Exception e){
			LogKit.error("添加模板失败：" + e.getMessage());
			return RetKit.fail();
		}

		String templetData = templet.getData();
		if(StrKit.isBlank(templetData)){
			templetData = String.format("[{\"elementType\":3}]\"");
		}
		templet.setCover(cover.substring(0, coverFileNameIndex) + "/" + coverFileName);

		boolean succ = templet.setGap(2f).setPaperType(Constant.PAPER_TYPE_1).setPrintDirection(Constant.PRINT_DIRECTION_0)
				.setData(templetData).setBlackLabelGap(2f).setBlackLabelOffset(2f)
				.setType(Constant.TEMPLET_TYPE_BUSINESS).setCreateTime(new Date()).setUserId(sysUser.getSysUserId()).save();
		if (succ) {
			String content = sysUser.getSysUserName() + "添加了id为" + templet.getId() + "的" + templet.getName() + "模板";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok() : RetKit.fail();
	}

	public RetKit update(Templet templet, SysUser sysUser, String ip) {
		String cover = templet.getCover();
		int coverFileNameIndex = cover.lastIndexOf("/");
		String coverFileName = cover.substring(coverFileNameIndex+1);
		try{
			coverFileName = AESKit.encrypt(coverFileName);
		}catch (Exception e){
			LogKit.error("添加模板失败：" + e.getMessage());
			return RetKit.fail();
		}

		templet.setCover(cover.substring(0, coverFileNameIndex) + "/" + coverFileName);

		boolean succ = templet.setUserId(sysUser.getSysUserId()).update();
		if(succ){
			String content = sysUser.getSysUserName() + "编辑了id为" + templet.getId()  +"的" + templet.getName() + "模板";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok().setOk("编辑成功") : RetKit.fail();
	}

	public RetKit del(String id, SysUser sysUser,String ip){
		Templet templet = Templet.dao.findById(id);
		boolean succ = templet.delete();
		if(succ){
			String content = sysUser.getSysUserName() + "删除了id为" + id +"的" + templet.getName() + "模板";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok().setOk("删除成功") : RetKit.fail();
	}

	private String getFileNameOfCover(String cover){
		int coverFileNameIndex = cover.lastIndexOf("/");
		return  cover.substring(coverFileNameIndex+1);

	}

}
