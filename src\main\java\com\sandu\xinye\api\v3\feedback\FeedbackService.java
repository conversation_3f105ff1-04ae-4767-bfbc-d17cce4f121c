package com.sandu.xinye.api.v3.feedback;

import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.v3.RetKit;
import com.sandu.xinye.common.model.Feedback;

import java.util.Date;

public class FeedbackService {
	
	public static final FeedbackService me = new FeedbackService();
	
	public RetKit addFeedBack(int userId ,String content,String contact,String img){
		Feedback fb= new Feedback();
		boolean succ = fb.setUserId(userId).setContent(content).setContact(contact).setImg(img)
				.setCreateTime(new Date()).setStatus(Constant.NOTDONE_FEEDBACK).save();
		return succ ? RetKit.ok() : RetKit.fail();
	}
}
