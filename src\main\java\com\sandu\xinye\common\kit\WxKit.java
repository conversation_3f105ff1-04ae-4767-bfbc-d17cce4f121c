package com.sandu.xinye.common.kit;

import com.alibaba.fastjson.JSON;
import com.google.gson.annotations.SerializedName;
import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.constant.WxConstant;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.json.WxGsonBuilder;
import me.chanjar.weixin.open.api.WxOpenService;
import me.chanjar.weixin.open.api.impl.WxOpenInMemoryConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenOAuth2ServiceImpl;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Serializable;
import java.net.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云OSS工具类
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
public class WxKit {
    private static final Logger logger = LoggerFactory.getLogger(WxKit.class);
    private static final WxOpenService wxOpenService;

    static {
        wxOpenService = new WxOpenOAuth2ServiceImpl(WxConstant.WX_APP_ID, WxConstant.WX_APP_SECRET);
        WxOpenInMemoryConfigStorage inMemoryConfigStorage = new WxOpenInMemoryConfigStorage();
        inMemoryConfigStorage.setComponentAppId(WxConstant.WX_APP_ID);
        inMemoryConfigStorage.setComponentAppSecret(WxConstant.WX_APP_SECRET);
        wxOpenService.setWxOpenConfigStorage(inMemoryConfigStorage);
    }

    public static WxOpenQueryAuthResult getQueryAuth(String auth_code) throws Exception {
        logger.info(String.format("WxConstant.WX_APP_ID: %s,, WxConstant.WX_APP_SECRET:%s",
                WxConstant.WX_APP_ID, WxConstant.WX_APP_SECRET));

        try {
            WxOpenQueryAuthResult queryAuthResult = wxOpenService.getWxOpenComponentService().getQueryAuth(auth_code);
            logger.info("getQueryAuth: %s", JSONUtil.toJsonStr(queryAuthResult));
            return queryAuthResult;
        } catch (WxErrorException e) {
            logger.error("gotoPreAuthUrl", e);
            throw new RuntimeException(e);
        }

    }

    public static WxOpenAuthorizerInfoResult getAuthorizerInfo() {
        logger.info(String.format("WxConstant.WX_APP_ID: %s,, WxConstant.WX_APP_SECRET:%s",
                WxConstant.WX_APP_ID, WxConstant.WX_APP_SECRET));
        try {
            WxOpenAuthorizerInfoResult authorizerInfoResult = wxOpenService.getWxOpenComponentService().getAuthorizerInfo(WxConstant.WX_APP_ID);
            logger.info("getAuthorerInfo: %s", JSONUtil.toJsonStr(authorizerInfoResult));
            return authorizerInfoResult;
        } catch (WxErrorException e) {
            logger.error("getAuthorizerInfo", e);
            throw new RuntimeException(e);
        }
    }

    public static WxOAuth2AccessToken getOAuthToken(String auth_code) throws Exception {
        try {
            String url = String.format("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code", WxConstant.WX_APP_ID, WxConstant.WX_APP_SECRET, auth_code);
            String responseContent = wxOpenService.get(url, null);
            return WxOAuth2AccessToken.fromJson(responseContent);
        } catch (WxErrorException e) {
            logger.error("getOAuthToken WxErrorException", e);
            throw new Exception(e);
        } catch (Exception e) {
            logger.error("getOAuthToken Exception", e);
            throw new Exception(e);
        }

    }

    public static WxOAuth2UserInfo getWxUserInfo(String accessToken, String openId) throws Exception {
        try {
            String url = String.format("https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s", accessToken, openId);
            String responseContent = wxOpenService.get(url, null);
            return WxOAuth2UserInfo.fromJson(responseContent);
        } catch (WxErrorException e) {
            logger.error("getWxUserInfo WxErrorException", e);
            throw new Exception(e);
        } catch (Exception e) {
            logger.error("getWxUserInfo Exception", e);
            throw new Exception(e);
        }

    }

    public static WxOAuth2AccessToken getQQOAuthToken(String auth_code, String redirect_uri) throws Exception {
        try {
            //获取access_token
            String url = String.format("https://graph.qq.com/oauth2.0/token?grant_type=authorization_code&client_id=%s&client_secret=%s&code=%s&redirect_uri=%s", WxConstant.QQ_APP_ID, WxConstant.QQ_APP_SECRET, auth_code, WxConstant.QQ_APP_REDIRECT);
            String responseContent = getData(url);
            //"access_token=03BEDC0F09F1A909EE3C41D31B11BC6A&expires_in=7776000&refresh_token=F754FA9B5B171E74939E18B76E26F2F7";
            String[] parts = responseContent.split("&");
            String accessToken = parts[0].split("=")[1];
            int expiresIn = Integer.parseInt(parts[1].split("=")[1]);
            String refreshToken = parts[2].split("=")[1];

            WxOAuth2AccessToken token = new WxOAuth2AccessToken();
            token.setAccessToken(accessToken);
            token.setRefreshToken(refreshToken);
            token.setExpiresIn(expiresIn);
            //获取openid
            String openIdUrl = String.format("https://graph.qq.com/oauth2.0/me?access_token=%s&fmt=json", accessToken);
            String responseContentOpen = getData(openIdUrl);
            //"callback( {"client_id":"102106292","openid":"BC6DBDF1A93F769384C44C3AFA1F819C"} )";
            WxOAuth2AccessToken wxOAuth2AccessToken = WxOAuth2AccessToken.fromJson(responseContentOpen);
            token.setOpenId(wxOAuth2AccessToken.getOpenId());
            return token;
        } catch (Exception e) {
            logger.error("getOAuthToken Exception", e);
            throw new Exception(e);
        }

    }

    /**
     * 网络请求 get
     *
     * @param url
     * @return
     */
    public static String getData(String url) {
        HttpURLConnection connection = null;
        try {
            URL apiUrl = new URL(url);
            connection = (HttpURLConnection) apiUrl.openConnection();
            connection.setRequestMethod("GET");

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuffer response = new StringBuffer();

                while ((inputLine = reader.readLine()) != null) {
                    response.append(inputLine);
                }
                reader.close();

                return response.toString();
            } else {
                return "";
            }
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (connection != null)
                connection.disconnect();

        }
        return "";
    }


    public static WxOAuth2UserInfo getQQUserInfo(String accessToken, String openId) throws Exception {
        try {
            //获取用户信息
            String url = String.format("https://graph.qq.com/user/get_user_info?access_token=%s&openid=%s&oauth_consumer_key=%s", accessToken, openId, WxConstant.QQ_APP_ID);
            String responseContent = getData(url);
            //获取unionId
            String unionIdurl = String.format("https://graph.qq.com/oauth2.0/me?access_token=%s&unionid=1&fmt=json", accessToken);
            String responseUnionContent = getData(unionIdurl);
            QQInfo wxOAuth2UserInfo = QQInfo.fromJson(responseContent);

            WxOAuth2UserInfo wxOAuth2Union = WxOAuth2UserInfo.fromJson(responseUnionContent);
            wxOAuth2Union.setNickname(wxOAuth2UserInfo.getNickname());
            wxOAuth2Union.setHeadImgUrl(wxOAuth2UserInfo.getAvatar());
            return wxOAuth2Union;
        } catch (Exception e) {
            logger.error("getWxUserInfo Exception", e);
            throw new Exception(e);
        }

    }


    public static String getAccessToken() throws Exception {
        try {
            String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", WxConstant.WX_APP_ID, WxConstant.WX_APP_SECRET);
            String responseContent = wxOpenService.get(url, null);
            LogKit.error("getAccessToken responseContent:" + responseContent);
            JSONObject jsonObject = JSONUtil.parseObj(responseContent);
            return jsonObject.get("access_token").toString();
        } catch (WxErrorException e) {
            logger.error("getWxUserInfo WxErrorException", e);
            throw new Exception(e);
        } catch (Exception e) {
            logger.error("getWxUserInfo Exception", e);
            throw new Exception(e);
        }

    }


    /**
     * 获取微信网页登录授权地址
     */
    public static String getWxAuthorizeUrl(String redirectUrl) {
        String redirectUrlEncoder = redirectUrl;
        try {
            redirectUrlEncoder = URLEncoder.encode(redirectUrl, "UTF-8");
        } catch (Exception e) {
        }

        return String.format("https://open.weixin.qq.com/connect/qrconnect?appid=%s" +
                "&redirect_uri=%s" +
                "&response_type=code&scope=snsapi_login&state=XPRINTER#wechat_redirect", WxConstant.WX_APP_ID, redirectUrlEncoder);
    }

    public static String getMpAccessToken() throws Exception {
        try {
            String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", "wx3a13bc8bd50328ff", "40bf927dbe9e12d46739539650171602");
            String responseContent = wxOpenService.get(url, null);
            LogKit.error("getAccessToken responseContent:" + responseContent);
            JSONObject jsonObject = JSONUtil.parseObj(responseContent);
            return jsonObject.get("access_token").toString();
        } catch (WxErrorException e) {
            logger.error("getWxUserInfo WxErrorException", e);
            throw new Exception(e);
        } catch (Exception e) {
            logger.error("getWxUserInfo Exception", e);
            throw new Exception(e);
        }

    }

    public static String buildRedirectUrl(String redirect, Map<String, Object> params) {
        StringBuilder builder = new StringBuilder();
        builder.append(redirect);

        if (!redirect.contains("?")) {
            builder.append("?");
        } else {
            builder.append("&");
        }

        builder.append(mapToQueryString(params));

        return builder.toString();
    }

    public static String buildRedirectUrl(String redirect, WxOAuth2UserInfo userInfo) {
        StringBuilder builder = new StringBuilder();
        builder.append(redirect);

        if (!redirect.contains("?")) {
            builder.append("?");
        } else {
            builder.append("&");
        }

        builder.append(mapUserToQueryString(userInfo));

        return builder.toString();
    }

    public static String mapUserToQueryString(WxOAuth2UserInfo userInfo) {
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("openId", userInfo.getOpenid());
        userMap.put("nickname", userInfo.getNickname());
        userMap.put("avatar", userInfo.getHeadImgUrl());
        userMap.put("unionId", userInfo.getUnionId());

        return mapToQueryString(userMap);
    }

    public static String mapToQueryString(Map<String, Object> map) {
        StringBuilder string = new StringBuilder();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            string.append(entry.getKey());
            string.append("=");
            try {
                String value = URLEncoder.encode((String) entry.getValue(), "UTF-8");
                string.append(value);
            } catch (Exception e) {
                string.append(entry.getValue());
            }
            string.append("&");
        }

        return string.toString();
    }


    public static class QQInfo implements Serializable {
        @SerializedName("figureurl_qq_1")
        private String avatar;
        @SerializedName("nickname")
        private String nickname;
        public static QQInfo fromJson(String json) {
            return (QQInfo) WxGsonBuilder.create().fromJson(json, QQInfo.class);
        }

        public String getAvatar() {
            return avatar;
        }

        public String getNickname() {
            return nickname;
        }
    }

}
