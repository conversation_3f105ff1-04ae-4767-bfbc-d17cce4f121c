package com.sandu.xinye.common.apple;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwk.Jwk;
import com.jfinal.kit.HttpKit;
import com.xiaoleilu.hutool.util.StrUtil;
import io.jsonwebtoken.*;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;

public class AppleUtil {

    private static final Logger logger = LoggerFactory.getLogger(AppleUtil.class);

    /**
     * 获取苹果的公钥
     *
     * @return
     * @throws Exception
     */
    private static JSONArray getAuthKeys() throws Exception {
        Map<String, String> headers = new HashMap<String, String>(16);
        headers.put("Content-Type", "application/json");
        String ret = HttpKit.get("https://appleid.apple.com/auth/keys", null, headers);
        logger.info("getAuthKeys->> 请求苹果授权key， 返回：{}", ret);
        JSONObject json = JSONObject.parseObject(ret);
        JSONArray arr = json.getJSONArray("keys");
        return arr;
    }

    public static String verify(String jwt) throws Exception {
        logger.info("verify->> jwt：{}", jwt);
        JSONArray arr = getAuthKeys();
        if (arr == null) {
            return "";
        }
        String userId = "";
        for (int i = 0; i < arr.size(); i++) {
            JSONObject authKey = null;
            authKey = JSONObject.parseObject(arr.getString(i));
            try {
                userId = verifyExc(jwt, authKey);
            } catch (Exception e) {
            }
            if (StrUtil.isNotEmpty(userId)) {
                break;
            }
        }
        return userId;
    }

    /**
     * 对前端传来的identityToken进行验证
     *
     * @param jwt     对应前端传来的 identityToken
     * @param authKey 苹果的公钥 authKey
     * @return
     * @throws Exception
     */
    public static String verifyExc(String jwt, JSONObject authKey) throws Exception {

        Jwk jwa = Jwk.fromValues(authKey);
        PublicKey publicKey = jwa.getPublicKey();

        String aud = "";
        String sub = "";
        if (jwt.split("\\.").length > 1) {
            String claim = new String(Base64.decodeBase64(jwt.split("\\.")[1]));
            aud = JSONObject.parseObject(claim).get("aud").toString();
            sub = JSONObject.parseObject(claim).get("sub").toString();
        }
        JwtParser jwtParser = Jwts.parser().setSigningKey(publicKey);
        jwtParser.requireIssuer("https://appleid.apple.com");
        jwtParser.requireAudience(aud);
        jwtParser.requireSubject(sub);

        try {
            Jws<Claims> claim = jwtParser.parseClaimsJws(jwt);
            if (claim != null && claim.getBody().containsKey("auth_time")) {
                return claim.getBody().getSubject();
            } else {
                return "";
            }
        } catch (ExpiredJwtException e) {
            logger.error("apple identityToken expired", e);
            return "";
        } catch (Exception e) {
            logger.error("apple identityToken illegal", e);
            return "";
        }
    }


    /**
     * 对前端传来的JWT字符串identityToken的第二部分进行解码
     * 主要获取其中的aud和sub，aud大概对应ios前端的包名，sub大概对应当前用户的授权的openID
     *
     * @param identityToken
     * @return {"aud":"com.xkj.****","sub":"000***.8da764d3f9e34d2183e8da08a1057***.0***","c_hash":"UsKAuEoI-****","email_verified":"true","auth_time":1574673481,"iss":"https://appleid.apple.com","exp":1574674081,"iat":1574673481,"email":"****@qq.com"}
     */
    public static JSONObject parserIdentityToken(String identityToken) {
        String[] arr = identityToken.split("\\.");
        Base64 base64 = new Base64();
        String decode = new String(base64.decodeBase64(arr[1]));
        String substring = decode.substring(0, decode.indexOf("}") + 1);
        JSONObject jsonObject = JSON.parseObject(substring);
        return jsonObject;
    }

    /**
     * 主方法，用于测试 AppleUtil 类中的方法
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            // 测试 getAuthKeys 方法
            System.out.println("测试 getAuthKeys 方法:");
            JSONArray authKeys = getAuthKeys();
            System.out.println(authKeys);

            // 测试 verify 方法
            System.out.println("\n测试 verify 方法:");
            String testJwt = "eyJraWQiOiJyczBNM2tPVjlwIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GlBsQB87nkyJ-4mga42C86YASAkGNF2KSkKP8esb40aqUmMPNtZ9qXP_gsKGeDmXWMr1vODmzIWJxdASjFAYtAFMbb__bwjx0dTRX8GHTZZMki6NG2ODXpoDriO4XHAoTnLtr1QQJ0G8DJUw6o8TwMhfD2JEhtpK-3qq6P9N8hvOTCc3Z8szYu5kFSDOFo-qq0_GMOyPb3vevoR81ME3dVmeGHbbgtHMZEWP5iSNY7OVeoZsq4D1YV73TLBx2LYW9oczis104Nr9xg2QYf8kYyUp3PoYcf9q_4q_-rY_iNKc2e82Xe8kl6rVY6nvdNyYX908-JSYjzlkUzrx9BDrOQ"; // 替换为实际的 JWT
            String userId = verify(testJwt);
            System.out.println("验证结果 (userId): " + userId);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}