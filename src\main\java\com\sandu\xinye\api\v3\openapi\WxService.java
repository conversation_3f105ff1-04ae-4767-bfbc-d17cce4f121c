package com.sandu.xinye.api.v3.openapi;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.Ret;
import com.sandu.xinye.common.kit.*;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import com.sandu.xinye.common.kit.v3.RetKit;


public class WxService {
    public static final WxService me = new WxService();

    /**
     * 微信网页登录，获取accessToken
     *
     * @return
     */
    public RetKit getAuthorizeToken(String code) {
        LogKit.info("getAuthorizeToken");

        try {
            WxOAuth2AccessToken result = WxKit.getOAuthToken(code);
            LogKit.info("getAuthorizeToken: " + result.getAccessToken());
            return RetKit.ok(result);
        } catch (Exception e) {
            LogKit.error("获取微信登录用户信息失败：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        }
    }


    /**
     * 微信网页登录，获取用户信息
     *
     * @return
     */
    public RetKit getWxUserInfo(String code) {
        LogKit.info("getWxUserInfo");

        try {
            // 获取token
            WxOAuth2AccessToken result = WxKit.getOAuthToken(code);
            // 获取用户信息
            WxOAuth2UserInfo userInfo = WxKit.getWxUserInfo(result.getAccessToken(), result.getOpenId());
            return RetKit.ok("data", userInfo);
        } catch (Exception e) {
            LogKit.error("获取微信登录用户信息失败：" + e.getMessage());
            return RetKit.fail(e.getMessage());
        }
    }

    public RetKit getAcessToken() {
        try {
            String token = WxKit.getAccessToken();
            return RetKit.ok("data", token);
        } catch (Exception e) {
            LogKit.error("获取微信登录用户信息失败：" + e.getMessage());
            return RetKit.fail(e.getMessage());
        }

    }

    public RetKit getAppUnionId(String openId) {
        try {
            // 1.0 获取移动端token
            String token = WxKit.getMpAccessToken();
            // 2.0 获取用户信息
            // 获取用户信息
            WxOAuth2UserInfo userInfo = WxKit.getWxUserInfo(token, openId);

            return RetKit.ok("data", userInfo.getUnionId());
        } catch (Exception e) {
            LogKit.error("获取微信登录用户信息失败：" + e.getMessage());
            return RetKit.fail(e.getMessage());
        }

    }
}
