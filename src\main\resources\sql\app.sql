#sql("font.paginate")
	select * from font
		where 1 = 1
		order by createTime desc
#end

#sql("machine.list")
	select machineId,machineName from machine
	where 1=1
	#if(sk.notBlank(name))
		and machineName like #p(name)
	#end
#end

#sql("templet.paginate")
    select t.*, ifnull(t.machineType, 0) AS machineType
    from (
             SELECT t.*
             FROM templet t
             where userId = #p(userId)
             union
             (
                 SELECT tt.*
                 FROM templet tt
                          inner join user uu on uu.userId = tt.userId and uu.wxUnionId is not null
                          left join user u on uu.wxUnionId = u.wxUnionId
                 where u.userId = #p(userId)
             )
         ) t
    where type != 1
	#if(sk.notBlank(groupId))
        and groupId = #p(groupId)
    #end
	#if(sk.notBlank(name))
		and name like #p(name)
	#end
    #if(sk.notBlank(widthBegin))
        and width >= #p(widthBegin)
    #end
    #if(sk.notBlank(widthEnd))
        and width <= #p(widthEnd)
    #end
    
#end

#sql("templet.share.paginate")
select t.*, ifnull(machineType, 0) as machineType from templet t
where  type != 1
	#if(sk.notNull(shareUser))
        and shareUser = #p(shareUser)
    #end
    #if(shareUser == null)
        and userId = #p(userId) and shareUser is not null
    #end
order by id desc
#end

#sql("templet.busi.paginate")
    select t.*, ifnull(machineType, 0) as machineType from templet t
	where type = 1
	#if(sk.notBlank(groupId))
    	and groupId = #p(groupId)
    #end
	#if(sk.notBlank(name))
		and ( name like #p(name)  OR eancode like #p(name) )
	#end
	#if(sk.notBlank(widthBegin))
        and width >= #p(widthBegin)
    #end
    #if(sk.notBlank(widthEnd))
        and width <= #p(widthEnd)
    #end
#end

#sql("logo.paginate")
SELECT g.logoId, g.logoImg,
#if(lang == 1)
    k.logoKindName as logoKindName
#else if(lang == 2)
    k.logoKindName as logoKindName
#else if(lang == 3)
    k.traditionalName as logoKindName
#else if(lang == 4)
    k.koreanName as logoKindName
#else
    k.logoKindName as logoKindName
#end
FROM logo g
    inner join logo_kind k on k.logoKindId = g.logoKindId
    where g.version = 2
        #if(sk.notBlank(kindId))
            and g.logoKindId = #p(kindId)
        #end
#end
