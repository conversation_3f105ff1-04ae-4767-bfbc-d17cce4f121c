package com.sandu.xinye.api.v3.templet;

import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.AESKit;
import com.sandu.xinye.common.kit.v3.RetKit;
import com.sandu.xinye.common.model.Templet;
import com.sandu.xinye.common.model.TempletGroup;
import com.xiaoleilu.hutool.util.StrUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class TempletBusiService {
    private static final org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(TempletBusiService.class);
    private Set<String> ALLOWED_SORT_FIELDS = new HashSet<>(Arrays.asList("updateTime", "name", "width", "height","createTime"));


    public static final TempletBusiService me = new TempletBusiService();

    public RetKit getTempletPage(int pageNumber, int pageSize, String name, String groupId, String indexRange,String sort,int sortType ) {
        if (StrKit.notBlank(name)) {
            name = "%" + name + "%";
        }
        if (StrKit.notBlank(sort)&&!ALLOWED_SORT_FIELDS.contains(sort)) {
            return RetKit.fail("Invalid sort by field: " + sort);
        }
        Kv kvParams = Kv.by("groupId", groupId).set("name", name);
        if (StrKit.notBlank(indexRange)) {
            String[] indexRangeArr = indexRange.split(",");
            kvParams.set("widthBegin", indexRangeArr[0]);
            if (indexRangeArr.length > 1) {
                kvParams.set("widthEnd", indexRangeArr[1]);
            }
        }
        String typeofSort="asc";
        if(sortType==1){
            typeofSort="desc";
        }
        SqlPara sqlPara = Db.getSqlPara("app.templet.busi.paginate", kvParams);
        String sql = sqlPara.getSql();
        if (StrKit.isBlank(sort)) {
            sqlPara.setSql(String.format(sql + "order by  id  asc",typeofSort));
        } else {
            sqlPara.setSql(String.format(sql + " order by  %s %s", sort,typeofSort));
        }
        Page<Templet> page = Templet.dao.paginate(pageNumber, pageSize, sqlPara);
        List<Templet> templets = page.getList();
        // 解密模板cover封面
        templets.stream().forEach(templet -> {
            try {
                String cover = decryptTempletCover(templet.getCover());
                templet.setCover(cover);
            } catch (Exception e) {
                logger.error(String.format("模板[%s]cover[%s]解密失败: \n%s ", templet.getId(), templet.getCover(), e.getMessage()));
            }
        });

        return RetKit.ok("page", page);
    }

    public RetKit getGroupList() {
        List<TempletGroup> list = getBusiTemplateGroupList();
        return RetKit.ok("list", list);
    }

    private List<TempletGroup> getBusiTemplateGroupList() {
        List<TempletGroup> list = TempletGroup.dao.find("select id as groupId,name from templet_group where type = 1 order by createTime  asc");
        return list;
    }

    private String decryptTempletCover(String originCover) {
        if (StrUtil.isEmpty(originCover)) {
            return "";
        }

        Integer coverNameIndex = originCover.lastIndexOf("/");
        String coverPrefix = originCover.substring(0, coverNameIndex + 1);
        String coverName = originCover.substring(coverNameIndex + 1);
        String decryptCover = originCover;
        try {
            String decryptCoverName = AESKit.decrypt(coverName);
            decryptCover = coverPrefix + decryptCoverName;
        } catch (Exception e) {
            logger.error(String.format("模板cover[%s]解密失败: \n%s ", originCover, e.getMessage()));
        }

        return decryptCover;
    }

}
