package com.sandu.xinye.api.v3.oss;

import com.jfinal.aop.Before;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.interceptor.CrossInterceptor;
import com.sandu.xinye.common.kit.RetKit;

import java.util.List;

public class OssController extends AppController {

    @Before({CrossInterceptor.class, AttackAntiInterceptor.class})
    public void uploadImg() {
        List<UploadFile> ufs = getFiles("file");
        RetKit ret = OssService.me.uploadImg(ufs);
        renderJson(ret);
    }

    @Before({CrossInterceptor.class, AttackAntiInterceptor.class})
    public void uploadExcel() {
        UploadFile uf = getFile("file");
        RetKit ret = OssService.me.uploadExcel(uf);
        renderJson(ret);
    }

    @Before({CrossInterceptor.class, AttackAntiInterceptor.class})
    public void updateExcel() {
        String ossUrl = getPara("ossUrl");
        UploadFile uf = getFile("file");
        RetKit ret = OssService.me.updateExcel(ossUrl, uf);
        renderJson(ret);
    }

    /**
     * @Title: getAssumeRole
     * @Description:
     * @date 2025/1/6
     * <AUTHOR>
     */
    public void getAssumeRole(){
        RetKit ret = OssService.me.getAssumeRole();
        renderJson(ret);
    }
}
