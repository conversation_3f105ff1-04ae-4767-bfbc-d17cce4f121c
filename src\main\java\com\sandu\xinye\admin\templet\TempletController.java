package com.sandu.xinye.admin.templet;

import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Templet;

public class TempletController extends AdminController {
	
	@Clear
	public void index(){
		render("/admin_index.html");
	}
	
	public void list(){
		int pageNumber = getParaToInt("pageNumber",1);
		int pageSize = getParaToInt("pageSize",10);
		RetKit ret = TempletService.me.list(pageSize, pageNumber,getParaToMap());
		renderJson(ret);
	}
	
	public void add(){
		Templet templet = getBean(Templet.class,"");
		RetKit ret = TempletService.me.add(templet, getAccount(),getIpAddress());
		renderJson(ret);
	}
	
	public void update(){
		Templet templet = getBean(Templet.class,"");
		RetKit ret = TempletService.me.update(templet, getAccount(),getIpAddress());
		renderJson(ret);
	}
	
	public void del(){
		String groupId = getPara(0);
		RetKit ret = TempletService.me.del(groupId, getAccount(),getIpAddress());
		renderJson(ret);
	}

}
