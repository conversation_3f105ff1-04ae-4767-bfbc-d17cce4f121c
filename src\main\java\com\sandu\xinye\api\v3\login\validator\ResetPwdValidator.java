package com.sandu.xinye.api.v3.login.validator;

import com.jfinal.core.Controller;
import com.sandu.xinye.common.validator.BaseValidator;

public class ResetPwdValidator extends BaseValidator {

	@Override
	protected void validate(Controller c) {
		validateParaNotNull("captcha", "验证码");
		validateParaNotNull("password", "密码");
//		validatePhone("phone", "msg", "手机号码格式不正确");
		validateParaNotNull("phone", "手机号");
	}

	@Override
	protected void handleError(Controller c) {
		renderErroJson(c);
	}

}
