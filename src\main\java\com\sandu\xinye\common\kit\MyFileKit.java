package com.sandu.xinye.common.kit;

import com.jfinal.kit.StrKit;

public class MyFileKit {

	private final static String[] excelExts = new String[]{"csv", "xls", "xlsx"};

	/**
	 * @Title: getExtName
	 * @Description:  获得文件后缀名
	 * @param fileName
	 * @return
	 * @date 2019年5月8日  下午4:57:47
	 * <AUTHOR>
	 */
	public static String getExtName(String fileName) {
		int index = fileName.lastIndexOf('.');
		if (index != -1 && (index + 1) < fileName.length()) {
			return fileName.substring(index + 1);
		} else {
			return null;
		}
	}

	/**
	 * 通过文件扩展名，判断是否为支持的excel文件，支持则返回 true，否则返回 false
	 */
	public static boolean isExcelExtName(String fileName) {
		if (StrKit.isBlank(fileName)) {
			return false;
		}
		fileName = fileName.trim().toLowerCase();
		String ext = MyFileKit.getExtName(fileName);
		if (ext != null) {
			for (String s : excelExts) {
				if (s.equals(ext)) {
					return true;
				}
			}
		}
		return false;
	}
}
