package com.sandu.xinye.api.v3.help;

import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.v3.RetKit;
import com.sandu.xinye.common.model.Help;

import java.util.List;

public class HelpService {

	public static final HelpService me = new HelpService();


	/**
	 * @Title: getHelp
	 * @Description:
	 * @param helpKind
	 * @return
	 * @date 2019年3月13日  上午10:53:42
	 * <AUTHOR>
	 */
	public RetKit getHelp(int helpKind) {
		List<Help> list = Help.dao.find("select * from web_help where helpKind = ?  order by createTime desc", helpKind);
		return RetKit.ok("list", list);
	}

	/**
	 * 国际化查询
	 *
	 * @param helpKind
	 * @return
	 */
	public RetKit getHelpI18n(int helpKind, String locale) {
		if (StrKit.isBlank(locale) || locale.equals(Constant.LOCALE_ZH_CN)) {
			return getHelp(helpKind);
		}

		List<Help> list = Help.dao.find("select hp.helpId, hp.machineId, hp.helpLogo, hp.helpKind, hp.sysUserId, hp.createTime, " +
				" ifnull(i18n.helpAnswer, hp.helpAnswer) as helpAnswer, \n" +
				" ifnull(i18n.helpName, hp.helpName) as helpName,\n" +
				" ifnull(i18n.helpVideo, hp.helpVideo) as helpVideo,\n" +
				" ifnull(i18n.link, hp.link) as link " +
				" from web_help hp" +
				" left join web_help_i18n i18n on i18n.helpId = hp.helpId and i18n.locale = ?" +
				" where hp.helpKind = ? " +
				" order by hp.createTime desc", locale, helpKind);
		return RetKit.ok("list", list);
	}
}
