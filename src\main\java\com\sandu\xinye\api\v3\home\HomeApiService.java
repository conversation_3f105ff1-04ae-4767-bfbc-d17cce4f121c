package com.sandu.xinye.api.v3.home;

import com.sandu.xinye.common.kit.v3.RetKit;
import com.sandu.xinye.common.model.Banner;

import java.util.List;

public class HomeApiService {

	public static final HomeApiService me = new HomeApiService();

	public RetKit list() {
		List<Banner> list = Banner.dao.find("select title, cover, type from banner where type = 1 or type = 2 order by id desc");
		return RetKit.ok("list", list);
	}

}
