package com.sandu.xinye.api.v3.common.validator;

import com.jfinal.core.Controller;
import com.sandu.xinye.common.validator.BaseValidator;

/**
 * <AUTHOR>
 */
public class CommonValidator extends BaseValidator {

	@Override
	protected void validate(Controller c) {
		validate<PERSON>hone("phone", "msg", "手机号码格式不正确");
		validateParaNotNull("phone", "手机号");
	}

	@Override
	protected void handleError(Controller c) {
		renderErroJson(c);
	}

}
