package com.sandu.xinye.api.v3.help;

import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.v3.RetKit;

public class HelpController extends AppController {
	


	/***
	 * 
	 * @Title
	 * @Description
	 * @Param helpKind   帮助类型  软件-1 硬件-2
	 * @return
	 * @time 2019年3月12日下午12:00:17
	 *
	 */
	@Clear
	public void getHelp(){
		int helpKind = getParaToInt("helpKind");
		RetKit ret = HelpService.me.getHelp(helpKind);
		renderJson(ret);
	}

	@Clear
	public void getHelpI18n(){
		int helpKind = getParaToInt("helpKind");
		String locale = getPara("locale");
		RetKit ret = HelpService.me.getHelpI18n(helpKind, locale);
		renderJson(ret);
	}
}
