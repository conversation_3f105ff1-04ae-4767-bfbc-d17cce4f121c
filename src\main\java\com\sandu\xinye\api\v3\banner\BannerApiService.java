package com.sandu.xinye.api.v3.banner;

import com.sandu.xinye.common.kit.v3.RetKit;
import com.sandu.xinye.common.model.Banner;

import java.util.List;

public class BannerApiService {

	public static final BannerApiService me = new BannerApiService();

	public RetKit list() {
		List<Banner> list = Banner.dao.find("select id, cover, createTime from banner where type = 0 order by id desc");
		return RetKit.ok("list", list);
	}

}
