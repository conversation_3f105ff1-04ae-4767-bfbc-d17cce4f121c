package com.sandu.xinye.api.user;

import com.jfinal.plugin.activerecord.Page;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;

public class UserService {

	public static final UserService me = new UserService();

	private static  final String BeginTime = "2022-04-08";

	public RetKit list(int pageNumber, int pageSize) {
		Page<User> page = getAppleUsers(pageNumber, pageSize);
		return RetKit.ok("page", page);
	}

	public RetKit updateAppleUser(String oldAppleUserId, String newAppleUserId) {
		User model = User.dao.findFirst("select * from user where appleLoginUserId = ? ", oldAppleUserId);
		if(model == null){
			return RetKit.fail("用户不存在！");
		}

		model.setAppleLoginUserId(newAppleUserId);

		boolean succ = model.update();
		return succ ? RetKit.ok() : RetKit.fail();
	}

	private Page<User> getAppleUsers(int pageNumber, int pageSize) {
		return User.dao.paginate(pageNumber, pageSize, "select * ",
				" from user where appleLoginUserId  is not null and lastLoginTime > ?", BeginTime);
	}


}
