package com.sandu.xinye.common.routes.v3;

import com.jfinal.config.Routes;
import com.sandu.xinye.api.v3.cloudfile.CloudfileController;
import com.sandu.xinye.api.v3.openapi.QQController;
import com.sandu.xinye.api.v3.openapi.WxController;
import com.sandu.xinye.api.v3.user.UserController;
import com.sandu.xinye.api.v3.about.AboutController;
import com.sandu.xinye.api.v3.app_version.AppVersionController;
import com.sandu.xinye.api.v3.banner.BannerApiController;
import com.sandu.xinye.api.v3.common.CommonController;
import com.sandu.xinye.api.v3.feedback.FeedbackController;
import com.sandu.xinye.api.v3.font.FontController;
import com.sandu.xinye.api.v3.help.HelpController;
import com.sandu.xinye.api.v3.home.HomeApiController;
import com.sandu.xinye.api.v3.login.UserLoginController;
import com.sandu.xinye.api.v3.logo.LogoController;
import com.sandu.xinye.api.v3.machine.MachineController;
import com.sandu.xinye.api.v3.oss.OssController;
import com.sandu.xinye.api.v3.survey.SurveyApiController;
import com.sandu.xinye.api.v3.templet.TempletBusiController;
import com.sandu.xinye.api.v3.templet.TempletController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;

public class ApiRoutes extends Routes {

    @Override
    public void config() {
        this.addInterceptor(new AppUserInterceptor());

        // 登录
        this.add("/api/v3/login", UserLoginController.class);
        // 设备
        this.add("/api/v3/device", MachineController.class);
        // banner
        this.add("/api/v3/banner", BannerApiController.class);
        // logo
        this.add("/api/v3/logo", LogoController.class);
        // 关于公司
        this.add("/api/v3/about", AboutController.class);
        // 帮助中心
        this.add("/api/v3/help", HelpController.class);
        // 字体
        this.add("/api/v3/font", FontController.class);
        // 意见反馈
        this.add("/api/v3/feedback", FeedbackController.class);
        // 通用
        this.add("/api/v3/common", CommonController.class);
        // 自定义模板
        this.add("/api/v3/templet", TempletController.class);
        // 行业模板
        this.add("/api/v3/templetbusi", TempletBusiController.class);
        // 版本
        this.add("/api/v3/appVersion", AppVersionController.class);
        // 问卷调查
        this.add("/api/v3/survey", SurveyApiController.class);
        // 用户请求
        this.add("/api/v3/user", UserController.class);
        // OSS请求
        this.add("/api/v3/oss", OssController.class);
        // web首页数据查询
        this.add("/api/v3/home", HomeApiController.class);
        // web首页数据查询
        this.add("/api/v3/cloudfile", CloudfileController.class);
        // openapi
        // 微信开放平台
        this.add("/api/v3/wx", WxController.class);
        this.add("/api/v3/qq", QQController.class);
    }

}
