package com.sandu.xinye.api.v3.openapi;

import com.jfinal.aop.Clear;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.constant.WxConstant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.CipherKit;
import com.sandu.xinye.common.kit.WxKit;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import com.xiaoleilu.hutool.util.StrUtil;
import io.netty.util.internal.StringUtil;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import org.slf4j.LoggerFactory;
import com.sandu.xinye.common.kit.v3.RetKit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;


/**
 * <AUTHOR>
 */
public class WxController extends AppController {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(WxController.class);

    @Clear
    public void authorize() {
        String code = getPara("code");
        String ip = getPara("ip");
        String timestamp = getPara("timestamp");
        // 签名，校验数据合法性
        String sign = getPara("sign");
        // 回调地址
        String redirect = getPara("redirect");
        try {
            if (StrUtil.isNotEmpty(redirect)) {
                redirect = URLDecoder.decode(redirect, "UTF-8");
            }
        } catch (Exception e) {
            logger.error("redirect解析错误：" + e.getMessage());
        }

        // 请求不合法，直接返回空
        String errorMsg = checkRequsetValid(ip, timestamp, sign);
        if (StrUtil.isNotEmpty(errorMsg)) {
            if (!StrKit.isBlank(redirect)) {
                JSONObject params = new JSONObject();
                params.put("error", errorMsg);
                String redirectTo = WxKit.buildRedirectUrl(redirect, params);
                redirect301(redirectTo, false);
            } else {
                RetKit ret = RetKit.fail(errorMsg);
                renderJson(ret);
            }
            return;
        }

        RetKit ret = WxService.me.getWxUserInfo(code);
        if (ret.success()) {
            try {
                WxOAuth2UserInfo userInfo = JSONUtil.toBean(JSONUtil.parseObj(ret.get("data")), WxOAuth2UserInfo.class);
                if (!StrKit.isBlank(redirect)) {
                    String redirectTo = WxKit.buildRedirectUrl(redirect, userInfo);
                    redirect301(redirectTo, false);
                    return;
                } else {
                    renderJson(userInfo);
                    return;
                }
            } catch (Exception e) {
                logger.error("获取用户信息失败：" + e.getMessage());
                renderNull();
                return;
            }
        }
    }

    private String checkRequsetValid(String ip, String timestamp, String sign) {

        Map<String, String> map = new TreeMap();
        map.put("ip", ip);
        map.put("timestamp", timestamp);

        if (StrKit.isBlank(ip) || StrKit.isBlank(timestamp) || StrKit.isBlank(sign)) {
            return "参数不完整，请重新扫码登录！";
        }

        // 判断时间是否失效
        long currentTimeStamp = System.currentTimeMillis();
        long timeStampL = 0;
        try {
            timeStampL = Long.parseLong(timestamp);
        } catch (Exception e) {
            return "时间戳不正确，请重新扫码登录！";
        }

        if (currentTimeStamp - timeStampL > Constant.ATTACH_ANTI_EXPIRE_TIMES) {
            return "请求已超时，请重新扫码登录！";
        }

        // 校验签名
        String calcSign = CipherKit.sign(map);
        if (!sign.equals(calcSign)) {
            return "请求校验失败，请重新扫码登录！";
        }

        return "";
    }

}
